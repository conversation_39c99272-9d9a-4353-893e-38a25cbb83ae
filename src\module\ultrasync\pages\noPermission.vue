<template>
    <div class="no-permission-container">
        <div class="no-permission-content">
            <div class="icon-container">
                <svg class="no-permission-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM13 17h-2v-6h2v6zm0-8h-2V7h2v2z" fill="#ff6b6b"/>
                </svg>
            </div>
            <h1 class="title">访问受限</h1>
            <p class="message">抱歉，您没有权限访问此页面</p>
            <div class="details">
                <p class="detail-item">
                    <span class="label">当前路由：</span>
                    <span class="value">{{ currentRoute }}</span>
                </p>
                <p class="detail-item" v-if="requiredPermissions && requiredPermissions.length > 0">
                    <span class="label">需要权限：</span>
                    <span class="value">{{ requiredPermissions.join('、') }}</span>
                </p>
                <p class="detail-item" v-if="errorMessage">
                    <span class="label">错误信息：</span>
                    <span class="value error-text">{{ errorMessage }}</span>
                </p>
            </div>
            <div class="actions">
                <button class="btn btn-primary" @click="goBack">返回上一页</button>
                <button class="btn btn-secondary" @click="goHome">返回首页</button>
            </div>
        </div>
    </div>
</template>

<script>
import { RoutePermissionManager } from '@/common/permission/index.js'
import Tool  from '@/common/tool'

export default {
    name: 'NoPermission',
    data() {
        return {
            currentRoute: '',
            requiredPermissions: [],
            errorMessage: ''
        }
    },

    mounted() {
        this.initializeData()
    },
    methods: {
        initializeData() {
            // 获取当前路由信息
            this.currentRoute = this.$route.query.route || this.$route.path

            // 获取错误信息
            this.errorMessage = this.$route.query.error || ''

            // 调试信息
            console.log('移动端NoPermission 页面初始化:', {
                currentRoute: this.currentRoute,
                fromRoute: this.$route.query.from,
                errorMessage: this.errorMessage,
                fullQuery: this.$route.query
            });

            // 获取所需权限信息
            if (this.$route.query.route) {
                const routeConfig = RoutePermissionManager.getRouteConfig(this.$route.query.route)
                if (routeConfig) {
                    // 处理 permissions 字段，可能是字符串、数组或对象
                    if (routeConfig.permissions) {
                        if (typeof routeConfig.permissions === 'string') {
                            this.requiredPermissions = [routeConfig.permissions]
                        } else if (Array.isArray(routeConfig.permissions)) {
                            this.requiredPermissions = routeConfig.permissions
                        } else if (typeof routeConfig.permissions === 'object') {
                            // 对象格式，尝试提取权限信息
                            this.requiredPermissions = this.extractPermissionsFromObject(routeConfig.permissions)
                        } else {
                            this.requiredPermissions = []
                        }
                    } else {
                        this.requiredPermissions = []
                    }
                }
            }
        },

        extractPermissionsFromObject(permissionsObj) {
            // 从权限对象中提取权限信息
            const permissions = []

            if (permissionsObj.featurePermissionKey) {
                permissions.push(`功能权限: ${permissionsObj.featurePermissionKey}`)
            }
            if (permissionsObj.regionPermissionKey) {
                permissions.push(`区域权限: ${permissionsObj.regionPermissionKey}`)
            }
            if (permissionsObj.conversationPermissionKey) {
                permissions.push(`会话权限: ${permissionsObj.conversationPermissionKey}`)
            }
            if (permissionsObj.permissions && Array.isArray(permissionsObj.permissions)) {
                permissions.push(...permissionsObj.permissions)
            }

            return permissions
        },

        goBack() {
            // 返回上一页
            try {
                // 检查是否有来源页面信息
                const fromRoute = this.$route.query.from;
                if (fromRoute && fromRoute !== '/no-permission') {
                    // 如果有明确的来源路由且不是无权限页面本身，直接跳转
                    console.log('移动端返回到来源页面:', fromRoute);
                    Tool.backToRoute(fromRoute);
                    return;
                }

                // 使用 Vue Router 的 go(-1) 方法返回上一页
                console.log('移动端使用浏览器历史记录返回上一页');
                this.$router.go(-1);

            } catch (error) {
                console.warn('移动端返回上一页失败，跳转到首页:', error);
                this.goHome();
            }
        },
        goHome() {
            // 返回首页
            Tool.backToRoute('/index')
        },
    }
}
</script>

<style lang="scss" scoped>
.no-permission-container {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 0.75rem;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;

    .no-permission-content {
        background: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        box-shadow: 0 0.625rem 1.875rem rgba(0, 0, 0, 0.1);
        text-align: center;
        max-width: 26rem;
        width: 100%;

        .icon-container {
            margin-bottom: 1.5rem;

            .no-permission-icon {
                width: 3.5rem;
                height: 3.5rem;
                margin: 0 auto;
            }
        }

        .title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .message {
            font-size: 0.9375rem;
            color: #7f8c8d;
            margin-bottom: 1.25rem;
            line-height: 1.5;
        }

        .details {
            background: #f8f9fa;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-bottom: 1.25rem;
            text-align: left;

            .detail-item {
                margin-bottom: 0.75rem;
                font-size: 0.8125rem;
                line-height: 1.4;

                &:last-child {
                    margin-bottom: 0;
                }

                .label {
                    font-weight: 600;
                    color: #34495e;
                }

                .value {
                    color: #7f8c8d;
                    word-break: break-all;

                    &.error-text {
                        color: #e74c3c;
                    }
                }
            }
        }

        .actions {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
            flex-wrap: wrap;

            .btn {
                padding: 0.5rem 1rem;
                border: none;
                border-radius: 0.375rem;
                font-size: 0.8125rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                min-width: 6.5rem;

                &.btn-primary {
                    background-color: #3498db;
                    color: white;

                    &:hover {
                        background-color: #2980b9;
                        transform: translateY(-0.125rem);
                    }
                }

                &.btn-secondary {
                    background-color: #95a5a6;
                    color: white;

                    &:hover {
                        background-color: #7f8c8d;
                        transform: translateY(-0.125rem);
                    }
                }
            }
        }
    }
}
</style>
