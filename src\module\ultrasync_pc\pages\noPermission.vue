<template>
    <div class="no-permission-container">
        <div class="no-permission-content">
            <div class="icon-container">
                <svg class="no-permission-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM13 17h-2v-6h2v6zm0-8h-2V7h2v2z" fill="#ff6b6b"/>
                </svg>
            </div>
            <h1 class="title">访问受限</h1>
            <p class="message">抱歉，您没有权限访问此页面</p>
            <div class="details">
                <p class="detail-item">
                    <span class="label">当前路由：</span>
                    <span class="value">{{ currentRoute }}</span>
                </p>
                <p class="detail-item" v-if="requiredPermissions && requiredPermissions.length > 0">
                    <span class="label">需要权限：</span>
                    <span class="value">{{ requiredPermissions.join('、') }}</span>
                </p>
                <p class="detail-item" v-if="errorMessage">
                    <span class="label">错误信息：</span>
                    <span class="value error-text">{{ errorMessage }}</span>
                </p>
            </div>
            <div class="actions">
                <button class="btn btn-primary" @click="goBack">返回上一页</button>
                <button class="btn btn-secondary" @click="goHome">返回首页</button>
            </div>
        </div>
    </div>
</template>

<script>
import { RoutePermissionManager } from '@/common/permission/index.js'

export default {
    name: 'NoPermission',
    data() {
        return {
            currentRoute: '',
            requiredPermissions: [],
            errorMessage: ''
        }
    },

    mounted() {
        this.initializeData()
    },
    methods: {
        initializeData() {
            // 获取当前路由信息
            this.currentRoute = this.$route.query.route || this.$route.path

            // 获取错误信息
            this.errorMessage = this.$route.query.error || ''

            // 调试信息
            console.log('NoPermission 页面初始化:', {
                currentRoute: this.currentRoute,
                fromRoute: this.$route.query.from,
                errorMessage: this.errorMessage,
                fullQuery: this.$route.query
            });

            // 获取所需权限信息
            if (this.$route.query.route) {
                const routeConfig = RoutePermissionManager.getRouteConfig(this.$route.query.route)
                if (routeConfig) {
                    // 处理 permissions 字段，可能是字符串、数组或对象
                    if (routeConfig.permissions) {
                        if (typeof routeConfig.permissions === 'string') {
                            this.requiredPermissions = [routeConfig.permissions]
                        } else if (Array.isArray(routeConfig.permissions)) {
                            this.requiredPermissions = routeConfig.permissions
                        } else if (typeof routeConfig.permissions === 'object') {
                            // 对象格式，尝试提取权限信息
                            this.requiredPermissions = this.extractPermissionsFromObject(routeConfig.permissions)
                        } else {
                            this.requiredPermissions = []
                        }
                    } else {
                        this.requiredPermissions = []
                    }
                }
            }
        },
        extractPermissionsFromObject(permissionObj) {
            // 从权限对象中提取权限信息用于显示
            const permissions = []

            if (permissionObj.featurePermissionKey) {
                permissions.push(permissionObj.featurePermissionKey)
            }

            if (permissionObj.regionPermissionKey) {
                permissions.push(permissionObj.regionPermissionKey)
            }

            if (permissionObj.conversationPermissionKey) {
                permissions.push(permissionObj.conversationPermissionKey)
            }

            if (permissionObj.permissions && Array.isArray(permissionObj.permissions)) {
                permissions.push(...permissionObj.permissions)
            }

            // 如果没有提取到任何权限，显示对象的字符串表示
            if (permissions.length === 0) {
                permissions.push(JSON.stringify(permissionObj))
            }

            return permissions
        },
        goBack() {
            // 返回上一页
            try {
                // 检查是否有来源页面信息
                const fromRoute = this.$route.query.from;
                if (fromRoute && fromRoute !== '/no-permission') {
                    // 如果有明确的来源路由且不是无权限页面本身，直接跳转
                    console.log('返回到来源页面:', fromRoute);
                    this.$router.push(fromRoute);
                    return;
                }

                // 使用 Vue Router 的 go(-1) 方法返回上一页
                console.log('使用浏览器历史记录返回上一页');
                this.$router.go(-1);

            } catch (error) {
                console.warn('返回上一页失败，跳转到首页:', error);
                this.goHome();
            }
        },
        goHome() {
            // 返回首页
            this.$router.push('/main/dashboard')
        },
    }
}
</script>

<style lang="scss" scoped>
.no-permission-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 20px;

    .no-permission-content {
        background: white;
        border-radius: 12px;
        padding: 40px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        text-align: center;
        max-width: 500px;
        width: 100%;

        .icon-container {
            margin-bottom: 24px;

            .no-permission-icon {
                width: 80px;
                height: 80px;
                margin: 0 auto;
            }
        }

        .title {
            font-size: 28px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 12px;
        }

        .message {
            font-size: 16px;
            color: #7f8c8d;
            margin-bottom: 32px;
            line-height: 1.5;
        }

        .details {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 32px;
            text-align: left;

            .detail-item {
                margin-bottom: 12px;
                font-size: 14px;
                line-height: 1.4;

                &:last-child {
                    margin-bottom: 0;
                }

                .label {
                    font-weight: 600;
                    color: #495057;
                    display: inline-block;
                    min-width: 80px;
                }

                .value {
                    color: #6c757d;
                    word-break: break-all;

                    &.error-text {
                        color: #dc3545 !important;
                        font-weight: 500;
                    }
                }
            }
        }

        .actions {
            display: flex;
            gap: 12px;
            justify-content: center;
            flex-wrap: wrap;

            .btn {
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;
                min-width: 100px;

                &:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                }

                &.btn-primary {
                    background-color: #007bff;
                    color: white;

                    &:hover {
                        background-color: #0056b3;
                    }
                }

                &.btn-secondary {
                    background-color: #6c757d;
                    color: white;

                    &:hover {
                        background-color: #545b62;
                    }
                }

                &.btn-info {
                    background-color: #17a2b8;
                    color: white;

                    &:hover {
                        background-color: #117a8b;
                    }
                }
            }
        }

        // 响应式设计
        @media (max-width: 480px) {
            padding: 24px;

            .title {
                font-size: 24px;
            }

            .actions {
                flex-direction: column;

                .btn {
                    width: 100%;
                }
            }
        }
    }
}
</style>
