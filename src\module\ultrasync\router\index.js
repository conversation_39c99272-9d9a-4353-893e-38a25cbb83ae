import Vue from 'vue'
import VueRouter from 'vue-router'
import Tool from '@/common/tool'
import { RoutePermissionManager } from '@/common/permission/index.js'
// const Login = resolve => require(['../components/login.vue'], resolve)
import Root from '../pages/root.vue'
import Login from '../pages/login.vue'
import Register from '../pages/register.vue'
import ForgetPassword from '../pages/forgetPassword.vue'
// import ChangePassword from '../components/changePassword.vue'
import Index from '../pages/index.vue'
import Setting from '../pages/setting.vue'
import BuildVersion from '../pages/buildVersion.vue'
import Applys from '../pages/applys.vue'
import Groups from '../pages/groups.vue'
import AddFriend from '../pages/addFriend.vue'
import AddGroup from '../pages/addGroup.vue'
import AddGroupDetail from '../pages/addGroupDetail.vue'
import WechatAddGroup from '../pages/wechatAddGroup.vue'
// import handleSchemesLinkToApp from '../pages/handleSchemesLinkToApp'
import SearchGroup from '../pages/searchGroup.vue'
import SearchDepartment from '../pages/searchDepartment.vue'
import ChatWindow from '../pages/chatWindow.vue'
import International from '../pages/international.vue'
import PersonalSetting from '../pages/personalSetting.vue'
import ModifyBasicInfo from '../pages/modifyBasicInfo.vue'
import Scanner from '../pages/scanner.vue'
import GalleryControler from '../pages/galleryControler.vue'
import ModifyPassword from '../pages/modifyPassword.vue'
import ModifyPhoto from '../pages/modifyPhoto.vue'
import AboutUltrasync from '../pages/aboutUltraSync.vue'
import GroupSetting from '../pages/groupSetting.vue'
import GroupAllFile from '../pages/groupAllFile.vue'
import GroupAllIworks from '../pages/groupAllIworks.vue'
import GroupAllAttendee from '../pages/groupAllAttendee.vue'
import GroupAddAttendee from '../pages/groupAddAttendee.vue'
import GroupDeleteAttendee from '../pages/groupDeleteAttendee.vue'
import GroupModifySubject from '../pages/groupModifySubject.vue'
import GroupModifyAnnounce from '../pages/groupModifyAnnounce.vue'
import UserFavorites from '../pages/userFavorites.vue'
import TransmitControler from '../pages/transmitControler.vue'
import VisitingCard from '../pages/visitingCard.vue'
import UltrasoundMachine from '../pages/ultrasoundMachine.vue'
import DeviceChangePatient from '../pages/deviceChangePatient.vue'
import DeviceStorageSetting from '../pages/deviceStorageSetting.vue'
import Repository from '../pages/library/repository.vue'
import RepositoryPost from '../pages/library/repositoryPost.vue'
import LoginSetting from '../pages/loginSetting.vue'
import LocalStorageFile from '../pages/localstorageFile.vue'
import RealtimeVideo from '../pages/realtimeVideo.vue'
import HistoryVersion from '../pages/historyVersion.vue'
import HistoryVersionIntroduce from '../pages/historyVersionIntroduce.vue'
import InviteRegistration from '../pages/inviteRegistration.vue'
// import ManageVideoMember from '../pages/manageVideoMember.vue'//废弃
import TransferGroup from '../pages/transferGroup.vue'
import ResetMobile from '../pages/resetMobile.vue'
import ResetLoginName from '../pages/resetLoginName.vue'
import ReservedConference from '../pages/reservedConference.vue'
import addReserved from '../pages/addReserved.vue'
import Faq from '../pages/faq.vue'
import ExamManager from '../pages/examManager.vue'
import NewExam from '../pages/newExam.vue'
import ProtocolTree from '../pages/protocolTree.vue'
import iWorksStatistics from '../pages/iworksStatistics.vue'
import BIData from '../pages/biData.vue'
import QRCodeCard from '../pages/qrcodeCard.vue'
import GroupQRCodeCard from '../pages/groupQRCodeCard.vue'
import GroupVisitingCard from '../pages/groupVisitingCard.vue'
import EditGroupset from '../pages/editGroupset.vue'
import EditGroupsetManager from '../pages/editGroupsetManager.vue'
import GroupsetSetting from '../pages/groupsetSetting.vue'
import ModifyGroupset from '../pages/modifyGroupset.vue'
import Groupsets from '../pages/groupsets.vue'
import IcloudSearch from '../pages/icloudSearch.vue'
import icloudSearchMore from '../pages/icloudSearchMore.vue'
// import HospitalSelect from '../pages/hospitalSelect.vue'
import ChatHistorySearchList from '../pages/chatHistorySearchList'
import ChatHistoryWindow from '../pages/chatHistoryWindow'
import ChatHistorySearchUser from '../pages/chatHistorySearchUser'
import ChatHistorySearchDate from '../pages/chatHistorySearchDate'
import DownloadFile from '../pages/downloadFile'
import PersonalPrivacy from '../pages/personalPrivacy'
import PrivacySettings from '../pages/privacySettings'
import CaseDatabase from '../pages/caseDatabase'
import loginVerify from '../pages/loginVerify'
import DestroyAuthentication from '../pages/destroyAuthentication'
// import RegisterWelcome from '../pages/registerWelcome'
import ReferralCode from '../pages/referralCode'
import LiveManagement from '../pages/live_management/live_management'
import LiveManagementMyLive from '../pages/live_management/live_management_my_live'
import LiveManagementHistory from '../pages/live_management/live_management_history'
import LiveDetail from '../pages/live_detail'
import LoginOrRegister from '../pages/loginOrRegister'
import Remark from '../pages/remark'
import ReferralIntroduce from '../pages/referralIntroduce'
import GroupCollection from '../pages/groupCollection'
import GroupsetDetail from '../pages/groupsetDetail'
import ScanToLogin from '../pages/scanToLogin'
import PushStreamSetting from '../pages/pushStreamSetting'
import SafeAuth from '../pages/safeAuth'
import DeviceBinding from '../pages/deviceBinding'
import DeviceList from '../pages/deviceList'
import StoreStateView from '../pages/storeStateView'
// import ExamImageList from '../pages/examImageList'
import MyDevice from '../pages/myDevice'
// import DeviceDetail from '../pages/deviceDetail'
import ShareLink from '../pages/shareLink'
import EditOrganization from '../pages/editOrganization'
import AddTagControler from '../pages/addTagControler'
import PublicFavorite from '../pages/publicFavorite'
import TreeSelectContacts from '../pages/treeSelectContacts'
import BookingLive from '../pages/live_management/booking_live'
import GroupManage from '../pages/groupManage'
import GroupManagers from '../pages/groupManagers'
import EditGroupManager from '../pages/editGroupManager'
import GroupJoinVerify from '../pages/groupJoinVerify'
import PersonalProfileSetting from '../pages/personalProfileSetting'
import Init from '../pages/init'
import GroupModifyNickname from '../pages/groupModifyNickname.vue'
import GroupsetManager from '../pages/groupsetManager.vue'
import CloudExam from '../pages/cloudExam/cloudExam.vue'
import CloudExamDetail from '../pages/cloudExam/exam.vue'
import CloudExamCorrecting from '../pages/cloudExam/correctingExam.vue'
import FileListPage from '../pages/fileListPage.vue'
import imageViewer from '../MRComponents/imageViewer.vue'
import AutoRecognitionThreshold from '../pages/autoRecognitionThreshold.vue'
import ULinkerConsultation from '../pages/uLinker_consultation.vue'
import NoPermission from '../pages/noPermission.vue'
const aiMain = () => import(/* webpackPrefetch: true */ '../pages/aiChat/aiMain.vue')
const aiChat = () => import(/* webpackPrefetch: true */ '../pages/aiChat/aiChat.vue')
const practiceOverview = () => import(/* webpackPrefetch: true */ '../pages/cloudExam/practiceOverview.vue')
const practiceHistory = () => import(/* webpackPrefetch: true */ '../pages/cloudExam/practiceHistory.vue')
const practiceDetail = () => import(/* webpackPrefetch: true */ '../pages/cloudExam/practiceDetail.vue')
const practiceAnswerAction = () => import(/* webpackPrefetch: true */ '../pages/cloudExam/practiceAnswerAction.vue')
const tempLiveRoom = () => import(/* webpackPrefetch: true */ '../pages/tempLiveRoom.vue')
const tvWallPage = () => import(/* webpackPrefetch: true */ '../pages/tvWallPage.vue')
const uLinkerInstructionManual = () => import(/* webpackPrefetch: true */ '../pages/uLinkerInstructionManual.vue')
const CloudExamStatistics = () => import(/* webpackPrefetch: true */ '../pages/cloudExam/examStatistics.vue')
const CloudExamSetting = () => import(/* webpackPrefetch: true */ '../pages/cloudExam/examSetting.vue')
const ClubEntry = () => import(/* webpackPrefetch: true */ '../pages/club/clubEntry.vue')
const ClubApply = () => import(/* webpackPrefetch: true */ '../pages/club/clubApply.vue')
const ClubIndex = () => import(/* webpackPrefetch: true */ '../pages/club/clubIndex.vue')
const ClubPost = () => import(/* webpackPrefetch: true */ '../pages/club/clubPost.vue')
const cloudExamPage = {
    path:'cloud_exam',
    name:'cloud_exam',
    component:CloudExam,
    children:[
        {
            path:'exam/:type/:id',
            name:'cloud_exam_detail',
            component:CloudExamDetail,
            children:[
                {
                    path: 'transmit/arrange',
                    name: 'arrange_transmit',
                    component: TransmitControler,
                    meta: {
                        callback: 'arrangeTransmitCallback',
                        tip: 'assign_homework_to',
                        title: 'assign_homework',
                        disableChat: true,
                        disableFriend: false,
                        disableGroup: false,
                    }
                },
                {
                    path: 'transmit/share',
                    name: 'share_transmit',
                    component: TransmitControler,
                    meta: {
                        callback: 'shareExamTransmitCallback',
                        tip: 'share_paper_to',
                        title: 'share_paper',
                        disableChat: true,
                        disableFriend: false,
                        disableGroup: true,
                    }
                },
                {
                    path:'exam_setting/:group_id',
                    name:'exam_setting',
                    component:CloudExamSetting
                },
                {
                    path:'image_viewer',
                    name:'image_viewer',
                    component:imageViewer
                }
            ]
        },
        {
            path:'correcting_exam/:role',
            name:'correcting_exam',
            component:CloudExamCorrecting,
            children:[
                {
                    path:'exam/:type/:id',
                    name:'cloud_exam_detail',
                    component:CloudExamDetail,
                    children:[
                        {
                            path:'image_viewer',
                            name:'image_viewer',
                            component:imageViewer
                        }
                    ]
                },
                {
                    path:'exam_statistics/:id',
                    name:'exam_statistics',
                    component:CloudExamStatistics
                },
                {
                    path:'exam_setting',
                    name:'exam_setting',
                    component:CloudExamSetting
                }
            ]
        },
        {
            path:'exam_statistics/:id',
            name:'exam_statistics',
            component:CloudExamStatistics
        }
    ]
}
const chatWindowPage = {
    path: 'chat_window/:cid',
    name: 'chat_window',
    component: ChatWindow,
    meta: {
        inChatWindow: true,
    },
    children: [
        {
            path: 'download/:file_id',
            name: 'downloadFile',
            component: DownloadFile,
        },
        {
            path: 'realtimeVideo',
            name: 'realtimeVideo',
            component: RealtimeVideo,
        },
        {
            path: 'gallery',
            name: 'chat_gallery',
            component: GalleryControler,
            meta: {
                isShowTransferBtn: true,
                isShowFavoriteBtn: true,
                isShowShareWechat: true,
                isShowAnalyze: true,
            },
            children: [
                {
                    path: 'transmit',
                    name: 'transmit',
                    component: TransmitControler,
                    meta: {
                        callback: 'generalTransmitSubmit',
                    },
                },
                {
                    path: 'add_custom_tag',
                    name: 'add_custom_tag',
                    component: AddTagControler,
                }
            ],
        },
        {
            path: 'group_setting',
            name: 'group_setting',
            component: GroupSetting,
            children: [
                {
                    path: 'gallery',
                    name: 'group_setting_gallery',
                    component: GalleryControler,
                    meta: {
                        isShowTransferBtn: true,
                        isShowFavoriteBtn: true,
                        isShowShareWechat: true,
                        isShowAnalyze: true,
                    },
                    children: [
                        {
                            path: 'transmit',
                            name: 'transmit',
                            component: TransmitControler,
                            meta: {
                                callback: 'generalTransmitSubmit',
                            },
                        },
                        {
                            path: 'add_custom_tag',
                            name: 'add_custom_tag',
                            component: AddTagControler,
                        }
                    ],
                },
                {
                    path: 'all_file',
                    name: 'all_file',
                    component: GroupAllFile,
                    children: [
                        {
                            path: 'gallery',
                            name: 'all_file_gallery',
                            component: GalleryControler,
                            meta: {
                                isShowTransferBtn: true,
                                isShowFavoriteBtn: true,
                                isShowShareWechat: true,
                                isShowAnalyze: true,
                            },
                            children: [
                                {
                                    path: 'transmit',
                                    name: 'transmit',
                                    component: TransmitControler,
                                    meta: {
                                        callback: 'generalTransmitSubmit',
                                    },
                                },
                                {
                                    path: 'add_custom_tag',
                                    name: 'add_custom_tag',
                                    component: AddTagControler,
                                }
                            ],
                        },
                        {
                            path: 'transmit',
                            name: 'transmit',
                            component: TransmitControler,
                            meta: {
                                callback: 'generalTransmitSubmit',
                            },
                        },
                        {
                            path: 'exam_manager',
                            name: 'exam_manager',
                            component: ExamManager,
                            children: [
                                {
                                    path: 'new_exam',
                                    name: 'new_exam',
                                    component: NewExam,
                                },
                            ],
                        },
                    ],
                },
                {
                    path: 'group_collection',
                    name: 'group_collection',
                    component: GroupCollection,
                    children: [
                        {
                            path: 'gallery',
                            name: 'group_collection_gallery',
                            component: GalleryControler,
                            meta: {
                                isShowTransferBtn: true,
                                isShowFavoriteBtn: true,
                                isShowShareWechat: true,
                                isShowAnalyze: true,
                            },
                            children: [
                                {
                                    path: 'transmit',
                                    name: 'transmit',
                                    component: TransmitControler,
                                    meta: {
                                        callback: 'generalTransmitSubmit',
                                    },
                                },
                                {
                                    path: 'add_custom_tag',
                                    name: 'add_custom_tag',
                                    component: AddTagControler,
                                }
                            ],
                        },
                        {
                            path: 'transmit',
                            name: 'transmit',
                            component: TransmitControler,
                            meta: {
                                callback: 'generalTransmitSubmit',
                            },
                        },
                    ]
                },
                {
                    path: 'all_iworks',
                    name: 'all_iworks',
                    component: GroupAllIworks,
                    children: [
                        {
                            path: 'protocol_tree/:guid/:nodeStr',
                            name: 'protocol_tree',
                            component: ProtocolTree,
                        },
                    ],
                },
                {
                    path: 'all_attendee',
                    name: 'all_attendee',
                    component: GroupAllAttendee,
                    children: [
                        {
                            path: 'visiting_card',
                            name: 'visiting_card',
                            component: VisitingCard,
                            meta: {
                                level: 5,
                            },
                            children:[
                                {
                                    path:'remark',
                                    name:'remark',
                                    component:Remark,
                                },
                                {
                                    path:'public_favorite/:uid',
                                    name:'public_favorite',
                                    component:PublicFavorite,
                                    children:[
                                        {
                                            path: 'gallery',
                                            name: 'gallery_public',
                                            component: GalleryControler,
                                            meta: {
                                                noneComment:true,
                                                hideLike:true
                                            },
                                        },
                                    ]
                                }
                            ]
                        },

                    ],
                },
                {
                    path: 'add_attendee',
                    name: 'add_attendee',
                    component: GroupAddAttendee,
                },
                {
                    path: 'delete_attendee',
                    name: 'delete_attendee',
                    component: GroupDeleteAttendee,
                },
                {
                    path: 'modify_subject',
                    name: 'modify_subject',
                    component: GroupModifySubject,
                },
                {
                    path: 'modify_group_nickname',
                    name: 'modify_group_nickname',
                    component: GroupModifyNickname,
                },
                {
                    path: 'modify_announce',
                    name: 'modify_announce',
                    component: GroupModifyAnnounce,
                },
                {
                    path: 'visiting_card',
                    name: 'visiting_card',
                    component: VisitingCard,
                    meta: {
                        level: 4,
                    },
                    children:[
                        {
                            path:'remark',
                            name:'remark',
                            component:Remark,
                        },
                        {
                            path:'public_favorite/:uid',
                            name:'public_favorite',
                            component:PublicFavorite,
                            children:[
                                {
                                    path: 'gallery',
                                    name: 'gallery_public',
                                    component: GalleryControler,
                                    meta: {
                                        noneComment:true,
                                        hideLike:true
                                    },
                                },
                            ]
                        }
                    ]
                },
                {
                    path: 'transmit',
                    name: 'transmit',
                    component: TransmitControler,
                    meta: {
                        callback: 'generalTransmitSubmit',
                    },
                },
                {
                    path: 'reserved_conference',
                    name: 'reserved_conference',
                    component: ReservedConference,
                    children: [
                        {
                            path: 'add',
                            name: 'add_reserved',
                            component: addReserved,
                        },
                    ],
                },
                {
                    path: 'exam_manager',
                    name: 'exam_manager',
                    component: ExamManager,
                    children: [
                        {
                            path: 'new_exam',
                            name: 'new_exam',
                            component: NewExam,
                        },
                    ],
                },
                {
                    path: 'protocol_tree/:guid/:nodeStr',
                    name: 'protocol_tree',
                    component: ProtocolTree,
                },
                {
                    path: 'group_qrcode_card',
                    name: 'group_qrcode_card',
                    component: GroupQRCodeCard,
                },
                {
                    path: 'chat_history_search_list',
                    name: 'chat_history_search_list',
                    component: ChatHistorySearchList,
                    children: [
                        {
                            path: 'user',
                            name: 'user',
                            component: ChatHistorySearchUser,
                        },
                        {
                            path: 'date',
                            name: 'date',
                            component: ChatHistorySearchDate,
                        },
                        {
                            path: 'chat_history_window_condition/:uid',
                            name: 'chat_history_window_condition',
                            component: ChatHistoryWindow,
                            children: [
                                {
                                    path: 'gallery',
                                    name: 'gallery_chat_history',
                                    component: GalleryControler,
                                    meta: {
                                        isShowTransferBtn: true,
                                        isShowFavoriteBtn: true,
                                        isShowShareWechat: true,
                                        isShowToGroup: false,
                                        isShowAnalyze: true,
                                    },
                                    children: [
                                        {
                                            path: 'transmit',
                                            name: 'transmit',
                                            component: TransmitControler,
                                            meta: {
                                                callback: 'generalTransmitSubmit',
                                            },
                                        },
                                        {
                                            path: 'add_custom_tag',
                                            name: 'add_custom_tag',
                                            component: AddTagControler,
                                        }
                                    ],
                                },
                                {
                                    path: 'ai_gallery',
                                    name: 'gallery_chat_history_gallery',
                                    component: GalleryControler,
                                    meta: {
                                        hideShowDownload: true,
                                        isShowToGroup: false,
                                        isShowTransferBtn: true,
                                        hideLike:true,
                                    },
                                    children: [
                                        {
                                            path: 'add_custom_tag',
                                            name: 'add_custom_tag',
                                            component: AddTagControler,
                                        },
                                        {
                                            path: 'transmit',
                                            name: 'transmit',
                                            component: TransmitControler,
                                            meta: {
                                                callback: 'generalTransmitSubmit',
                                            },
                                        },
                                    ],
                                },
                                cloudExamPage
                            ],
                        },
                        {
                            path: 'chat_history_window',
                            name: 'chat_history_window',
                            component: ChatHistoryWindow,
                            children: [
                                {
                                    path: 'gallery',
                                    name: 'gallery_chat_history',
                                    component: GalleryControler,
                                    meta: {
                                        isShowTransferBtn: true,
                                        isShowFavoriteBtn: true,
                                        isShowShareWechat: true,
                                        isShowToGroup: false,
                                        isShowAnalyze: true,
                                    },
                                    children:[
                                        {
                                            path: 'transmit',
                                            name: 'transmit',
                                            component: TransmitControler,
                                            meta: {
                                                callback: 'generalTransmitSubmit',
                                            },
                                        },
                                        {
                                            path: 'add_custom_tag',
                                            name: 'add_custom_tag',
                                            component: AddTagControler,
                                        }
                                    ]
                                },
                                {
                                    path: 'ai_gallery',
                                    name: 'gallery_chat_history_gallery',
                                    component: GalleryControler,
                                    meta: {
                                        isShowTransferBtn: true,
                                        hideShowDownload: true,
                                        hideLike:true,
                                    },
                                    children: [
                                        {
                                            path: 'add_custom_tag',
                                            name: 'add_custom_tag',
                                            component: AddTagControler,
                                        },
                                        {
                                            path: 'transmit',
                                            name: 'transmit',
                                            component: TransmitControler,
                                            meta: {
                                                callback: 'generalTransmitSubmit',
                                            },
                                        },
                                    ],
                                },
                                cloudExamPage
                            ],
                        },
                    ],
                },
                {
                    path: 'bi_data',
                    name: 'bi_data',
                    component: BIData,
                },
                {
                    path:'device_list',
                    name:'device_list',
                    component:DeviceList
                },
                {
                    path:'group_manage',
                    name:'group_manage',
                    component:GroupManage,
                    children:[
                        {
                            path: 'transfer_group',
                            name: 'transfer_group',
                            component: TransferGroup,
                        },
                        {
                            path: 'group_managers',
                            name: 'group_managers',
                            component: GroupManagers,
                            children:[
                                {
                                    path: 'edit_manager/:type',
                                    name: 'edit_manager',
                                    component: EditGroupManager,
                                },
                            ]
                        },
                        {
                            path: 'join_verify',
                            name: 'join_verify',
                            component: GroupJoinVerify,
                            children: [
                                {
                                    path: 'visiting_card',
                                    name: 'visiting_card',
                                    component: VisitingCard,
                                    meta: {
                                        level: 5,
                                    },
                                    children:[
                                        {
                                            path:'remark',
                                            name:'remark',
                                            component:Remark,
                                        },
                                        {
                                            path:'public_favorite/:uid',
                                            name:'public_favorite',
                                            component:PublicFavorite,
                                            children:[
                                                {
                                                    path: 'gallery',
                                                    name: 'gallery_public',
                                                    component: GalleryControler,
                                                    meta: {
                                                        noneComment:true,
                                                        hideLike:true
                                                    },
                                                },
                                            ]
                                        }
                                    ]
                                },
                            ],
                        }
                    ]
                }
            ],
        },
        {
            path: 'transmit',
            name: 'transmit',
            component: TransmitControler,
            meta: {
                callback: 'generalTransmitSubmit',
            },
        },
        {
            path: 'visiting_card',
            name: 'visiting_card',
            component: VisitingCard,
            meta: {
                level: 3,
            },
            children:[
                {
                    path:'remark',
                    name:'remark',
                    component:Remark,
                },
                {
                    path:'public_favorite/:uid',
                    name:'public_favorite',
                    component:PublicFavorite,
                    children:[
                        {
                            path: 'gallery',
                            name: 'gallery_public',
                            component: GalleryControler,
                            meta: {
                                noneComment:true,
                                hideLike:true
                            },
                        },
                    ]
                }
            ]
        },
        {
            path: 'ai_gallery',
            name: 'ai_gallery',
            component: GalleryControler,
            meta: {
                hideShowDownload: true,
                isShowTransferBtn: true,
                hideLike:true,
            },
            children: [
                {
                    path: 'add_custom_tag',
                    name: 'add_custom_tag',
                    component: AddTagControler,
                },
                {
                    path: 'transmit',
                    name: 'transmit',
                    component: TransmitControler,
                    meta: {
                        callback: 'generalTransmitSubmit',
                    },
                },
            ],
        },
        {
            path: 'exam_manager',
            name: 'exam_manager',
            component: ExamManager,
            children: [
                {
                    path: 'new_exam',
                    name: 'new_exam',
                    component: NewExam,
                },
            ],
        },
        {
            path: 'protocol_tree/:guid/:nodeStr',
            name: 'protocol_tree',
            component: ProtocolTree,
        },
        {
            path: 'share_link/:resource_ids/:type',
            name: 'ShareLink',
            component: ShareLink,
        },
        {
            path: 'case_database',
            component: CaseDatabase,
            name: 'case_database',
            meta: {
                isShowShareWechat: true,
            },
        },
        {
            path: 'homework_transmit',
            name: 'homework_transmit',
            component: TransmitControler,
            meta: {
                callback: 'homeworkTransmitCallback',
                disableChat:true,
                disableFriend:true,
            },
        },
        cloudExamPage,
    ],
}
Vue.use(VueRouter)
const router = new VueRouter({
    routes: [
        {
            path: '/',
            name: 'Root',
            component: Root,
        },
        {
            path: '/login',
            name: 'Login',
            component: Login,
            children: [
                // {
                //     path: 'register_welcome',
                //     name: 'RegisterWelcome',
                //     component: RegisterWelcome,
                //     children:[
                //         {
                //             path: 'register/:type',
                //             name: 'Register',
                //             component: Register,
                //         }
                //     ]
                // },
                {
                    path: 'register',
                    name: 'Register',
                    component: Register,
                },
                {
                    path: 'forget_password/:type',
                    name: 'ForgetPassword',
                    component: ForgetPassword,
                },
                // {
                //     path: 'change_password',
                //     name: 'ChangePassword',
                //     component: ChangePassword,
                // },
                {
                    path: 'setting',
                    name: 'loginSetting',
                    component: LoginSetting,
                    children: [
                        {
                            path: 'store_state_view',
                            name:'store_state_view',
                            component: StoreStateView,
                        },
                        {
                            path: 'international',
                            component: International,
                        },
                    ]
                },
                {
                    path:'login_verify',
                    name:loginVerify,
                    component:loginVerify
                },
                {
                    path: 'personalPrivacy',
                    name: 'PersonalPrivacy',
                    component: PersonalPrivacy,
                },
                {
                    path: 'referral_code',
                    name: 'referralCode',
                    component: ReferralCode,
                    meta:{
                        isShowLogin:true
                    },
                    children:[
                        {
                            path: 'referral_introduce',
                            name: 'referralIntroduce',
                            component: ReferralIntroduce,
                        }
                    ]
                },
                {
                    path:'login_or_register/:type',
                    name:'loginOrRegister',
                    component:LoginOrRegister,
                }
            ],
        },
        {
            path: '/index',
            name:'index',
            component: Index,
            meta: {
                keepAlive: true
            },
            children: [
                {
                    path: 'setting',
                    name: 'setting',
                    component: Setting,
                    children: [
                        {
                            path: 'international',
                            component: International,
                        },
                        {
                            path: 'localStorage_file',
                            component: LocalStorageFile,
                        },
                        {
                            path: 'push_stream_setting',
                            component: PushStreamSetting,
                        },
                        {
                            path: 'store_state_view',
                            component: StoreStateView,
                            name:'store_state_view'
                        },
                        {
                            path: 'auto_recognition_threshold',
                            component: AutoRecognitionThreshold,
                            name:'auto_recognition_threshold'
                        },
                    ],
                },
                {
                    path: 'personal_setting',
                    name: 'personal_setting',
                    component: PersonalSetting,
                    children: [
                        {
                            path: 'modify_basic_info',
                            component: ModifyBasicInfo,
                            // children: [
                            //     {
                            //         path: 'hospital_select',
                            //         name: 'hospital_select',
                            //         component: HospitalSelect,
                            //     },
                            // ],
                        },
                        {
                            path:'safe_auth/:type',
                            name:'safe_auth',
                            component:SafeAuth
                        },
                        {
                            path: 'reset_mobile/:type',
                            name: 'reset_mobile',
                            component: ResetMobile,
                        },
                        {
                            path: 'reset_login_name',
                            name: 'reset_login_name',
                            component: ResetLoginName,
                        },
                        {
                            path: 'modify_password',
                            component: ModifyPassword,
                        },
                        {
                            path: 'modify_photo',
                            component: ModifyPhoto,
                            props: true
                        },
                        {
                            path: 'qrcode_card',
                            component: QRCodeCard,
                        },
                        {
                            path:'destroy_authentication',
                            component:DestroyAuthentication
                        },
                        {
                            path:'edit_organization',
                            component:EditOrganization
                        },
                        {
                            path:'personal_profile_setting',
                            component:PersonalProfileSetting
                        },
                    ],
                },
                {
                    path: 'modify_password',
                    component: ModifyPassword,
                },
                chatWindowPage,
                {
                    path: 'groups',
                    name: 'groups',
                    component: Groups,
                },
                {
                    path: 'applys',
                    name: 'applys',
                    component: Applys,
                },
                {
                    path: 'add_friend',
                    name: 'addFriend',
                    component: AddFriend,
                    children:[
                        {
                            path: 'visiting_card',
                            name: 'visiting_card',
                            component: VisitingCard,
                            meta: {
                                level: 3,
                            },
                            children:[
                                {
                                    path:'remark',
                                    name:'remark',
                                    component:Remark,
                                },
                                {
                                    path:'public_favorite/:uid',
                                    name:'public_favorite',
                                    component:PublicFavorite,
                                    children:[
                                        {
                                            path: 'gallery',
                                            name: 'gallery_public',
                                            component: GalleryControler,
                                            meta: {
                                                noneComment:true,
                                                hideLike:true
                                            },
                                        },
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    path: 'add_group',
                    name: 'addGroup',
                    component: AddGroup,
                    children:[
                        {
                            path: 'add_group_detail',
                            name: 'addGroupDetail',
                            component: AddGroupDetail,
                        },
                    ],
                },

                {
                    path: 'wechat_add_group',
                    name: 'wechatAddGroup',
                    component: WechatAddGroup,
                },
                // {
                //     path: 'handle_schemes',
                //     name: 'handleSchemesLinkToApp',
                //     component: handleSchemesLinkToApp,
                // },
                {
                    path: 'search_group',
                    name: 'searchGroup',
                    component: SearchGroup,
                },
                {
                    path: 'icloud_search',
                    name: 'icloudSearch',
                    component: IcloudSearch,
                    children: [
                        {
                            path: 'icloud_search_more',
                            name: 'icloudSearchMore',
                            component: icloudSearchMore,
                        },
                    ],
                },
                {
                    path: 'scanner',
                    name: 'scanner',
                    component: Scanner,
                },
                {
                    path: 'gallery',
                    name: 'gallery',
                    component: GalleryControler,
                    meta: {
                        isShowTransferBtn: true,
                        isShowFavoriteBtn: true,
                        isShowShareWechat: true,
                        isShowToGroup: true,
                        isShowAnalyze: true,
                    },
                    children: [
                        {
                            path: 'transmit',
                            name: 'transmit',
                            component: TransmitControler,
                            meta: {
                                callback: 'generalTransmitSubmit',
                            },
                        },
                        {
                            path: 'add_custom_tag',
                            name: 'add_custom_tag',
                            component: AddTagControler,
                        }
                    ],
                },
                {
                    path: 'about_ultrasync',
                    name: 'about_ultrasync',
                    component: AboutUltrasync,
                    children: [
                        {
                            path: 'history_version',
                            name: 'history_version',
                            component: HistoryVersion,
                            children: [
                                {
                                    path: 'history_version_introduce',
                                    name: 'history_version_introduce',
                                    component: HistoryVersionIntroduce,
                                },
                            ],
                        },
                        {
                            path: 'faq',
                            name: 'faq',
                            component: Faq,
                        },
                        {
                            path: 'build_version',
                            name: 'build_version',
                            component: BuildVersion,
                        },
                        {
                            path: 'personalPrivacy',
                            name: 'personalPrivacy',
                            component: PersonalPrivacy,
                        },
                        {
                            path: 'privacySettings',
                            name: 'privacySettings',
                            component: PrivacySettings,
                        },
                        {
                            path:'InstructionManualVersion',
                            name:'InstructionManualVersion',
                            component: () => import('../pages/instructionManualVersion.vue'),
                            children:[
                                {
                                    path: 'InstructionManual',
                                    name: 'InstructionManual',
                                    component: () => import('../pages/instructionManual.vue'),
                                },
                            ]
                        },
                        {
                            path: 'uLinkerInstructionManual',
                            name: 'uLinkerInstructionManual',
                            component: () => import('../pages/uLinkerInstructionManual.vue'),
                        },

                    ],
                },
                {
                    path: 'user_favorites',
                    name: 'user_favorites',
                    component: UserFavorites,
                    children: [
                        {
                            path: 'gallery',
                            name: 'user_favorites_gallery',
                            component: GalleryControler,
                            meta: {
                                noneComment: true,
                                is_private: true,
                                isShowTransferBtn: true,
                                isShowToGroup: true,
                                isShowAnalyze: false,
                                hideLike:true,
                            },
                            children: [
                                {
                                    path: 'transmit',
                                    name: 'transmit',
                                    component: TransmitControler,
                                    meta: {
                                        callback: 'favoritesGalleryTransmit',
                                    },
                                },
                            ],
                        },
                        {
                            path: 'transmit',
                            name: 'transmit',
                            component: TransmitControler,
                            meta: {
                                callback: 'favoritesTransmit',
                            },
                        },
                    ],
                },
                {
                    path: 'visiting_card',
                    name: 'visiting_card',
                    component: VisitingCard,
                    meta: {
                        level: 2,
                    },
                    children:[
                        {
                            path:'remark',
                            name:'remark',
                            component:Remark,
                        },
                        {
                            path:'public_favorite/:uid',
                            name:'public_favorite',
                            component:PublicFavorite,
                            children:[
                                {
                                    path: 'gallery',
                                    name: 'gallery_public',
                                    component: GalleryControler,
                                    meta: {
                                        noneComment:true,
                                        hideLike:true
                                    },
                                },
                            ]
                        }
                    ]
                },
                {
                    path: 'group_visiting_card',
                    name: 'group_visiting_card',
                    component: GroupVisitingCard,
                    meta: {
                        level: 2,
                    },
                },
                {
                    path: 'ultrasound_machine',
                    name: 'ultrasound_machine',
                    component: UltrasoundMachine,
                    meta: {
                        inMachinePage: true,
                    },
                    children: [
                        {
                            path: 'gallery',
                            name: 'machine_gallery',
                            component: GalleryControler,
                            meta: {
                                noneComment: true,
                                isShowShareWechat: true,
                                shareLocalFile: true,
                                isShowTransferBtn: true,
                            },
                            children: [
                                {
                                    path: 'transmit',
                                    name: 'transmit',
                                    component: TransmitControler,
                                    meta: {
                                        callback: 'machineGalleryTransmit',
                                    },
                                },
                            ],
                        },
                        {
                            path: 'saved_image_gallery',
                            name: 'saved_image_gallery',
                            component: GalleryControler,
                            meta: {
                                noneComment: true,
                                isShowTransferBtn: false,
                            },
                        },
                        {
                            path: 'transmit',
                            name: 'transmit',
                            component: TransmitControler,
                            meta: {
                                callback: 'machineTransmit',
                            },
                        },
                        {
                            path: 'toggle_conversation',
                            name: 'device_toggle_conversation',
                            component: TransmitControler,
                            meta: {
                                callback: 'deviceToggleConversation',
                                tip: 'toggle_conversation_to',
                            },
                        },
                        {
                            path: 'patient/new',
                            name: 'patient_new',
                            component: DeviceChangePatient,
                            meta: {
                                canEditPatientId: true,
                            },
                        },
                        {
                            path: 'patient/edit/:device_id',
                            name: 'patient_edit',
                            component: DeviceChangePatient,
                            meta: {
                                canEditPatientId: false,
                            },
                        },
                        {
                            path: 'storage/:device_id',
                            name: 'storage',
                            component: DeviceStorageSetting,
                        },
                        {
                            path:'device_binding/:device_id',
                            name:'device_binding',
                            component:DeviceBinding
                        },
                        {
                            path: 'search_department',
                            name: 'searchDepartment',
                            component: SearchDepartment,
                        },
                    ],
                },
                {
                    path: 'transmit',
                    name: 'transmit',
                    component: TransmitControler,
                    meta: {
                        callback: 'generalTransmitSubmit',
                    },
                },
                {
                    path: 'repository',
                    component: Repository,
                    name: 'repository',
                    children: [
                        {
                            path: 'post/:post_id',
                            name: 'repository_post',
                            component: RepositoryPost,
                        },
                    ],
                },
                {
                    path: 'case_database',
                    component: CaseDatabase,
                    name: 'case_database',
                    meta: {
                        isShowShareWechat: true,
                    },

                },
                {
                    path: 'exam_manager',
                    name: 'exam_manager',
                    component: ExamManager,
                    children: [
                        {
                            path: 'new_exam',
                            name: 'new_exam',
                            component: NewExam,
                        },
                    ],
                },
                {
                    path: 'invite_registration',
                    name: 'invite_registration',
                    component: InviteRegistration,
                },
                // {
                //     path: 'iworks_statistics',
                //     name: 'iworks_statistics',
                //     component: iWorksStatistics,
                // },

                // {
                //     path: 'groupset_setting',
                //     name: 'groupset_setting',
                //     component: GroupsetSetting,
                //     children: [

                //         {
                //             path: 'edit_groupset/:type/:step',
                //             name: 'edit_groupset',
                //             component: EditGroupset,
                //         },
                //     ],
                // },
                {
                    path: 'groupsets',
                    name: 'groupsets',
                    component: Groupsets,
                    children:[
                        {
                            path: 'edit_groupset/:type/:step',
                            name: 'add_groupset',
                            component: EditGroupset,
                        },
                        {
                            path: 'detail/:id',
                            name: 'groupset_detail',
                            component: GroupsetDetail,
                            children:[
                                {
                                    path: 'iworks_statistics',
                                    name: 'iworks_statistics',
                                    component: iWorksStatistics,
                                },
                                {
                                    path: 'bi_data',
                                    name: 'bi_data',
                                    component: BIData,
                                },
                                {
                                    path:'groupset_setting',
                                    name:'groupset_setting',
                                    component:GroupsetSetting,
                                    children:[
                                        {
                                            path: 'modify/:type',
                                            name: 'modify',
                                            component: ModifyGroupset,
                                        },
                                        {
                                            path: 'edit_groupset/:type/:step',
                                            name: 'edit_groupset',
                                            component: EditGroupset,
                                        },
                                        {
                                            path:'groupset_manager',
                                            name:'groupset_manager',
                                            component:GroupsetManager,
                                            children:[
                                                {
                                                    path:'edit_groupset_manager/:action',
                                                    name:'edit_groupset_manager',
                                                    component:EditGroupsetManager,
                                                },
                                            ]
                                        }
                                    ]
                                },

                            ]
                        },
                    ]
                },
                {
                    path: 'ai_main',
                    name: 'ai_main',
                    component: aiMain,
                    redirect: '/index/ai_main/ai_chat',
                    children:[
                        {
                            path:'ai_chat',
                            name:'ai_chat',
                            component: aiChat,
                        },
                        {
                            path:'practice_overview',
                            name:'ai_chat_practice_overview',
                            component: practiceOverview,
                            children:[
                                {
                                    path:'practice_history',
                                    name:'ai_chat_practice_history',
                                    component: practiceHistory,
                                    children:[
                                        {
                                            path:'practice_detail',
                                            name:'ai_chat_practice_history_practice_detail',
                                            component: practiceDetail,
                                        },
                                        {
                                            path:'practice_answer_action',
                                            name:'ai_chat_practice_history_practice_answer_action',
                                            component: practiceAnswerAction,
                                        }
                                    ]
                                },
                                {
                                    path:'practice_detail',
                                    name:'ai_chat_practice_detail',
                                    component: practiceDetail,
                                },
                                {
                                    path:'practice_answer_action',
                                    name:'ai_chat_practice_answer_action',
                                    component: practiceAnswerAction,
                                }
                            ]
                        }
                    ]
                },
                {
                    path: 'live_management',
                    name: 'live_management',
                    component: LiveManagement,
                    children:[
                        {
                            path: 'my_live',
                            name: 'my_live',
                            component: LiveManagementMyLive,
                            children:[
                                chatWindowPage,
                                {
                                    path: 'live_detail/:live_id',
                                    name: 'live_detail',
                                    component: LiveDetail,
                                },
                                {
                                    path: 'booking_live',
                                    name: '/index/live_management/my_live/booking_live',
                                    component: BookingLive,
                                    children: [
                                        {
                                            path:'treeSelectContacts',
                                            name: '/index/live_management/my_live/booking_live/treeSelectContacts',
                                            component: TreeSelectContacts,
                                        }
                                    ],

                                }
                            ]
                        },
                        {
                            path: 'history',
                            name: 'history',
                            component: LiveManagementHistory,
                            children: [
                                {
                                    path: 'gallery',
                                    name: 'live_management_history_gallery',
                                    component: GalleryControler,
                                    meta: {
                                        noneComment: true,
                                    },
                                },
                            ],
                        },
                        {
                            path: 'booking_live',
                            name: '/index/live_management/booking_live',
                            component: BookingLive,
                            children: [
                                {
                                    path:'treeSelectContacts',
                                    name: '/index/live_management/booking_live/treeSelectContacts',
                                    component: TreeSelectContacts,
                                }
                            ],

                        }
                    ]
                },
                {
                    path:'scan_to_login/:id',
                    name:'scan_to_login',
                    component: ScanToLogin,
                    children:[
                        {
                            path: 'personalPrivacy',
                            name: 'PersonalPrivacy',
                            component: PersonalPrivacy,
                        },
                    ]
                },
                {
                    path:'my_device',
                    name:'my_device',
                    component:MyDevice,
                    children:[
                        {
                            path:'device_binding/:device_id',
                            name:'device_binding',
                            component:DeviceBinding
                        },
                    ]
                },
                {
                    path: 'referral_code',
                    name: 'referralCode',
                    component: ReferralCode,
                    meta:{
                        isShowLogin:false
                    },
                    children:[
                        {
                            path: 'referral_introduce',
                            name: 'referralIntroduce',
                            component: ReferralIntroduce,
                        }
                    ]
                },
                {
                    path: 'temp_live_room/:id',
                    name: 'temp_live_room',
                    component: tempLiveRoom,
                },
                {
                    path:'tv_wall_page',
                    name:'tv_wall_page',
                    component: tvWallPage,
                },
                cloudExamPage,
                {
                    path:'practice_overview',
                    name:'index_practice_overview',
                    component: practiceOverview,
                    children:[
                        {
                            path:'practice_history',
                            name:'index_practice_history',
                            component: practiceHistory,
                            children:[
                                {
                                    path:'practice_detail',
                                    name:'index_practice_history_practice_detail',
                                    component: practiceDetail,
                                },
                                {
                                    path:'practice_answer_action',
                                    name:'index_practice_history_practice_answer_action',
                                    component: practiceAnswerAction,
                                }
                            ]
                        },
                        {
                            path:'practice_detail',
                            name:'index_practice_detail',
                            component: practiceDetail,
                        },
                        {
                            path:'practice_answer_action',
                            name:'index_practice_answer_action',
                            component: practiceAnswerAction,
                        }
                    ]
                },
                {
                    path:'club_entry',
                    name:'club_entry',
                    component: ClubEntry,
                    children:[
                        {
                            path:'club_apply/:club_name/:status',
                            name:'club_apply',
                            component: ClubApply,
                            children:[
                                {
                                    path: 'personalPrivacy',
                                    name: 'PersonalPrivacy',
                                    component: PersonalPrivacy,
                                },
                            ]
                        },
                        {
                            path:'club_index/:club_name',
                            name:'club_index',
                            component: ClubIndex,
                            children: [
                                {
                                    path: 'post/:post_id',
                                    name: 'club_post',
                                    component: ClubPost,
                                },
                            ],
                        }
                    ]
                },
                {
                    path:'file_list',
                    name:'file_list',
                    component: FileListPage,
                    children:[
                        {
                            path: 'gallery',
                            name: 'gallery',
                            component: GalleryControler,
                            meta: {
                                isShowTransferBtn: true,
                                isShowFavoriteBtn: true,
                                isShowShareWechat: true,
                                isShowToGroup: true,
                                isShowAnalyze: true,
                            },
                            children: [
                                {
                                    path: 'transmit',
                                    name: 'transmit',
                                    component: TransmitControler,
                                    meta: {
                                        callback: 'generalTransmitSubmit',
                                    },
                                },
                                {
                                    path: 'add_custom_tag',
                                    name: 'add_custom_tag',
                                    component: AddTagControler,
                                }
                            ],
                        },
                        {
                            path: 'transmit',
                            name: 'transmit',
                            component: TransmitControler,
                            meta: {
                                callback: 'generalTransmitSubmit',
                            },
                        },
                    ]
                }
            ],
        },
        // {
        //     path: '/scan_device',
        //     name: 'scan_device',
        //     component: ScanDevice,
        //     children: [
        //         {
        //             path: 'scanner',
        //             name: 'unlogin_scanner',
        //             component: Scanner,
        //         },
        //         {
        //             path: 'ultrasound_machine',
        //             name: 'unlogin_ultrasound_machine',
        //             component: UltrasoundMachine,
        //             children: [
        //                 {
        //                     path: 'gallery',
        //                     name: 'unlogin_gallery',
        //                     component: GalleryControler,
        //                     meta: {
        //                         isShowShareWechat: true,
        //                         shareLocalFile: true,
        //                         noneComment: true,
        //                     },
        //                 },
        //             ],
        //         },
        //     ],
        // },
        {
            path: '/personalPrivacy',
            name: 'personalPrivacy',
            component: PersonalPrivacy,
        },
        {
            path: '/uLinkerInstructionManual',
            name: 'uLinkerInstructionManual',
            component: uLinkerInstructionManual,
        },
        {
            path: '/init',
            name: 'Init',
            component: Init,
        },
        {
            path: '/no-permission',
            name: 'NoPermission',
            component: NoPermission
        },
        {
            path: '/uLinker_consultation',
            name: 'uLinker_consultation',
            component: ULinkerConsultation
        }
    ],
})

//解决vue-router3.0重复访问同一路由导致的报错问题
let originalPush = VueRouter.prototype.push
let originalReplace  = VueRouter.prototype.replace

VueRouter.prototype.push = function push(location) {
    // 获取目标路径
    let targetPath;
    if (typeof location === 'string') {
        targetPath = location;
    } else {
        targetPath = location.path || location.name;
    }

    // 分割路径并进行检查
    if (targetPath) {
        const segments = targetPath.split('/').filter(segment => segment !== '');
        const lastSegment = segments[segments.length - 1];
        const secondLastSegment = segments[segments.length - 2];
        // 检查路径段是否为数字或字符串形式的数字
        const isNumeric = (str) => !isNaN(str) && !isNaN(parseFloat(str));
        if (lastSegment && secondLastSegment && lastSegment === secondLastSegment &&!(isNumeric(lastSegment) || isNumeric(secondLastSegment))) {
            console.log('检测到重复的路径段，拒绝访问:', targetPath);
            return Promise.reject(new Error('重复的路径段，拒绝访问'));
        }
    }

    // 调用原始的 push 方法
    return originalPush.call(this, location).catch(e => e);
};
VueRouter.prototype.replace = function push(location){
    return originalReplace.call(this,location).catch(e=>e)
}

// 移动端白名单（如果需要的话）
const whiteList = ['/login', '/register', '/forgetPassword'];

// 初始化路由权限管理器
try {
    // 设置路由权限管理器的路由配置
    const routePermissionManager = RoutePermissionManager.getGlobalInstance();
    routePermissionManager.setRouterConfig(router, whiteList);
    console.log('移动端路由权限管理器初始化完成');
} catch (error) {
    console.warn('移动端路由权限管理器初始化失败:', error);
}

export default router
