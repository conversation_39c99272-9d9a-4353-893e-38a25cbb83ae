<template>
    <div>
        <popover name="menu" event="longtap" :show="menuDatas.length > 0" @hide="menuDatas = []">
            <tooltips-menu
                :menuDatas="menuDatas"
                @copyText="copyText"
                @revocationMessage="revocationMessage"
                @multiSelectImage="multiSelectImage"
                @setResourceExamInfo="setResourceExamInfo"
                @shareToWechat="shareToWechat"
                @saveFavorite="openFavoriteSelectAction"
                @transmit="transmit"
                @sendToAnalyze="sendToAnalyzeFn"
                @deleteChatMessages="deleteChatMessages"
                @shareToEmail="shareToEmail"
                @editReviewInfo="editReviewInfo"
                @searchInCaseData="searchInCaseData"
                @imageRename="imageRename"
                @deleteExam="handleDeleteExam"
                @quoteMessage="quoteMessage"
                @locateOriginalMessage="locateOriginalMessage"
            />
        </popover>
        <ImageRenameDialog :message="currentFile" v-model="imageRenameVisible"> </ImageRenameDialog>
        <van-action-sheet
            v-model="isShowSelectFavoriteType"
            :actions="selectFavoriteTypeActions"
            @select="onSelectFavoriteType"
            close-on-click-action
            :cancel-text="$t('cancel_btn')"
            @cancel="isShowSelectFavoriteType = false"
        />
        <ReviewEditDialog :message="currentFile" ref="reviewEditDialog"></ReviewEditDialog>
        <CommonDialog
            v-model="groupFavoriteDialog"
            :title="$t('group_favorite_text')"
            :showRejectButton="true"
            :beforeClose="submitInsertGroupFavorite"
        >
            <group-favorite
                :edit="editMode"
                :cid="cid"
                ref="chatMessageListGroupFavorite"
                v-if="groupFavoriteDialog"
            ></group-favorite>
        </CommonDialog>
    </div>
</template>
<script>
import Tool from "@/common/tool";
import { Toast } from "vant";
import base from "../lib/base";
import tooltipsMenu from "../MRComponents/tooltipsMenu.vue";
import ImageRenameDialog from "./imageRenameDialog.vue";
import ReviewEditDialog from "./messageItem/reviewEditDialog.vue";
import GroupFavorite from "../components/groupFavorite.vue";

import CommonDialog from "../MRComponents/commonDialog.vue";
import { initImportExamImageTempQueue } from "../lib/common_send_message";
import { ActionSheet } from "vant";
import service from "../service/service";
import { sendToAnalyze } from "../lib/common_base";
import { htmlEscape } from "../lib/common_base";
import { MENU_ITEMS } from "../lib/constants.js";
export default {
    props: {
        currentFile: {
            type: Object,
            default: () => {
                return {};
            },
        },
        value: {
            type: Boolean,
            default: false,
        },
    },
    mixins: [base],
    components: {
        VanActionSheet: ActionSheet,
        tooltipsMenu,
        ImageRenameDialog,
        ReviewEditDialog,
        GroupFavorite,
        CommonDialog,
    },
    computed: {
        conversation() {
            return this.$store.state.conversationList[this.cid] || {};
        },
        cid() {
            return this.$route.query.cid || this.$route.params.cid;
        },
    },
    data() {
        return {
            menuDatas: [],
            imageRenameVisible: false,
            isShowSelectFavoriteType: false,
            selectFavoriteTypeActions: [],
            groupFavoriteDialog: false,
            editMode: true,
            from: "",
        };
    },
    created() {
        this.selectFavoriteTypeActions = [
            { name: this.$t('personal_favorite_text'), key: 0 },
            { name: this.$t('group_favorite_text'), key: 1 },
        ];
    },
    methods: {
        // ==================== 权限检查方法 ====================
        /**
         * 检查消息权限的通用方法
         * @param {string} action - 权限动作 (withdraw, delete, quote, transmit等)
         * @param {Object} message - 消息对象
         * @returns {boolean}
         */
        checkMessagePermission(action, message) {
            return this.$checkPermission(
                { conversationPermissionKey: `message.${action}` },
                {
                    conversationId: this.cid,
                    message: message
                }
            );
        },

        /**
         * 检查资源权限的通用方法
         * @param {string} action - 权限动作 (rename, delete, share等)
         * @param {Object} message - 消息对象
         * @returns {boolean}
         */
        checkResourcePermission(action, message) {
            return this.$checkPermission(
                { conversationPermissionKey: `resource.${action}` },
                {
                    conversationId: this.cid,
                    message: message
                }
            );
        },

        /**
         * 检查检查权限的通用方法
         * @param {string} action - 权限动作 (delete, send_to_analyze等)
         * @param {Object} message - 消息对象
         * @returns {boolean}
         */
        checkExamPermission(action, message) {
            return this.$checkPermission(
                { conversationPermissionKey: `exam.${action}` },
                {
                    conversationId: this.cid,
                    message: message
                }
            );
        },

        /**
         * 检查会话权限的通用方法
         * @param {string} action - 权限动作 (edit_settings等)
         * @returns {boolean}
         */
        checkConversationPermission(action) {
            return this.$checkPermission(
                { conversationPermissionKey: `conversation.${action}` },
                {
                    conversationId: this.cid
                }
            );
        },

        /**
         * 替代原有的checkShowItemWhenHasPermission方法
         * 检查是否可以对消息进行操作（撤回或删除）
         * @param {Object} message - 消息对象
         * @returns {boolean}
         */
        canOperateMessage(message) {
            return this.checkMessagePermission('withdraw', message) ||
                   this.checkMessagePermission('delete', message);
        },

        /**
         * 替代原有的checkShowWithDraw方法
         * 检查是否可以撤回消息（包含时间限制）
         * @param {Object} message - 消息对象
         * @returns {boolean}
         */
        canWithdrawMessage(message) {
            return this.checkMessagePermission('withdraw', message);
        },

        /**
         * 检查是否可以删除消息
         * @param {Object} message - 消息对象
         * @returns {boolean}
         */
        canDeleteMessage(message) {
            return this.checkMessagePermission('delete', message);
        },

        // ==================== 菜单构建方法 ====================
        /**
         * 添加菜单项（带权限检查）
         * @param {number} menuKey - 菜单项key
         * @param {string} permissionType - 权限类型 (message/resource/exam/conversation)
         * @param {string} action - 权限动作
         * @param {Object} msg - 消息对象
         * @param {Function} extraCheck - 额外检查函数
         */
        addMenuItemWithPermission(menuKey, permissionType, action, msg, extraCheck = null) {
            let hasPermission = false;

            if (permissionType === 'message') {
                hasPermission = this.checkMessagePermission(action, msg);
            } else if (permissionType === 'resource') {
                hasPermission = this.checkResourcePermission(action, msg);
            } else if (permissionType === 'exam') {
                hasPermission = this.checkExamPermission(action, msg);
            } else if (permissionType === 'conversation') {
                hasPermission = this.checkConversationPermission(action);
            }

            // 额外检查条件
            if (hasPermission && extraCheck) {
                hasPermission = extraCheck();
            }

            if (hasPermission) {
                this.menuDatas.push(menuKey);
            }
        },

        /**
         * 构建图片/视频菜单
         */
        buildImageVideoMenu(msg, from) {
            // 转发 - 所有用户都可以
            this.addMenuItemWithPermission(MENU_ITEMS.TRANSMIT, 'message', 'transmit', msg);

            // 收藏 - 所有用户都可以
            this.addMenuItemWithPermission(MENU_ITEMS.SAVE_FAVORITE, 'resource', 'save_favorite', msg);

            // 多选 - 特定场景下显示
            if (from !== "examImageItem") {
                this.addMenuItemWithPermission(MENU_ITEMS.MULTI_SELECT, 'resource', 'multi_select', msg);
            }

            // AI分析 - 需要特殊检查
            this.addMenuItemWithPermission(MENU_ITEMS.SEND_TO_ANALYZE, 'exam', 'send_to_analyze', msg, () => this.checkShowSendToAnalyze(msg));

            // 图片搜索 - 需要特殊检查
            this.addMenuItemWithPermission(MENU_ITEMS.IMAGE_SEARCH, 'exam', 'image_search', msg, () => this.checkShowImageSearch());

            // 引用消息 - 需要gmsg_id
            if (msg.gmsg_id && from === "chatComponent") {
                this.addMenuItemWithPermission(MENU_ITEMS.QUOTE, 'message', 'quote', msg);
            }

            // 撤回/删除 - 根据权限决定
            if (this.canWithdrawMessage(msg)) {
                this.menuDatas.push(MENU_ITEMS.WITHDRAW); // 撤回
            } else if (this.canDeleteMessage(msg)) {
                this.menuDatas.push(MENU_ITEMS.DELETE); // 删除
            }

            // 重命名 - 需要权限
            this.addMenuItemWithPermission(MENU_ITEMS.RENAME, 'resource', 'rename', msg);

            // 分享微信 - 需要开启微信分享（无权限检查，直接添加）
            if (this.isShowWechat) {
                this.menuDatas.push(MENU_ITEMS.SHARE_WECHAT);
            }
        },

        /**
         * 构建标签/评论菜单
         */
        buildTagCommentMenu(msg) {
            // 撤回 - 只有撤回权限
            this.addMenuItemWithPermission(MENU_ITEMS.WITHDRAW, 'message', 'withdraw', msg);
        },

        /**
         * 构建文本菜单
         */
        buildTextMenu(msg, from) {
            // 复制文本
            this.addMenuItemWithPermission(MENU_ITEMS.COPY, 'message', 'copy', msg);

            // 引用消息
            if (msg.gmsg_id && from === "chatComponent") {
                this.addMenuItemWithPermission(MENU_ITEMS.QUOTE, 'message', 'quote', msg);
            }

            // 撤回
            this.addMenuItemWithPermission(MENU_ITEMS.WITHDRAW, 'message', 'withdraw', msg);
        },

        /**
         * 构建声音菜单
         */
        buildSoundMenu(msg) {
            // 撤回
            this.addMenuItemWithPermission(MENU_ITEMS.WITHDRAW, 'message', 'withdraw', msg);
        },

        /**
         * 构建文件菜单
         */
        buildFileMenu(msg) {
            // 转发 - 文件未过期
            if (!this.checkFileExpired(msg)) {
                this.addMenuItemWithPermission(MENU_ITEMS.TRANSMIT, 'message', 'transmit', msg);
            }

            // 撤回/删除
            if (this.canWithdrawMessage(msg)) {
                this.menuDatas.push(MENU_ITEMS.WITHDRAW);
            } else if (this.canDeleteMessage(msg)) {
                this.menuDatas.push(MENU_ITEMS.DELETE);
            }
        },

        /**
         * 构建AI分析菜单
         */
        buildAIAnalyzeMenu(msg) {
            // 转发 - 未撤回且有资源ID
            if (!msg.been_withdrawn && msg.resource_id) {
                this.addMenuItemWithPermission(MENU_ITEMS.TRANSMIT, 'message', 'transmit', msg);
            }

            // 撤回
            this.addMenuItemWithPermission(MENU_ITEMS.WITHDRAW, 'message', 'withdraw', msg);
        },

        /**
         * 构建检查图片菜单
         */
        buildExamImagesMenu(msg) {
            // 转发
            this.addMenuItemWithPermission(MENU_ITEMS.TRANSMIT, 'message', 'transmit', msg);

            // 邮箱分享
            this.addMenuItemWithPermission(MENU_ITEMS.SHARE_EMAIL, 'resource', 'share_email', msg);

            // AI分析
            this.addMenuItemWithPermission(MENU_ITEMS.SEND_TO_ANALYZE, 'exam', 'send_to_analyze', msg, () => this.checkShowSendToAnalyze(msg));

            // 分享微信（无权限检查，直接添加）
            if (this.isShowWechat) {
                this.menuDatas.push(MENU_ITEMS.SHARE_WECHAT);
            }
        },

        /**
         * 构建作业菜单
         */
        buildHomeworkMenu(msg) {
            // 转发
            this.addMenuItemWithPermission(MENU_ITEMS.TRANSMIT, 'message', 'transmit', msg);

            // 撤回
            this.addMenuItemWithPermission(MENU_ITEMS.WITHDRAW, 'message', 'withdraw', msg);
        },

        /**
         * 构建普通视频菜单 (Video/Cine)
         */
        buildVideoMenu(msg, from) {
            // 转发 - 所有用户都可以
            this.addMenuItemWithPermission(MENU_ITEMS.TRANSMIT, 'message', 'transmit', msg);

            // 收藏 - 所有用户都可以
            this.addMenuItemWithPermission(MENU_ITEMS.SAVE_FAVORITE, 'resource', 'save_favorite', msg);

            // 多选 - 特定场景下显示
            if (from !== "examImageItem") {
                this.addMenuItemWithPermission(MENU_ITEMS.MULTI_SELECT, 'resource', 'multi_select', msg);
            }

            // 引用消息 - 需要gmsg_id
            if (msg.gmsg_id && from === "chatComponent") {
                this.addMenuItemWithPermission(MENU_ITEMS.QUOTE, 'message', 'quote', msg);
            }

            // 撤回/删除 - 根据权限决定
            if (this.canWithdrawMessage(msg)) {
                this.menuDatas.push(MENU_ITEMS.WITHDRAW); // 撤回
            } else if (this.canDeleteMessage(msg)) {
                this.menuDatas.push(MENU_ITEMS.DELETE); // 删除
            }

            // 重命名 - 需要权限
            this.addMenuItemWithPermission(MENU_ITEMS.RENAME, 'resource', 'rename', msg);

            // 分享微信 - 需要开启微信分享（无权限检查，直接添加）
            if (this.isShowWechat) {
                this.menuDatas.push(MENU_ITEMS.SHARE_WECHAT);
            }

            // 注意：普通视频不包含以下功能：
            // - AI分析 (SEND_TO_ANALYZE)
            // - 图片搜索 (IMAGE_SEARCH)
        },

        /**
         * 构建视频剪辑菜单 - 简化版本，只包含基本功能
         */
        buildVideoClipMenu(msg, from) {
            // 转发 - 所有用户都可以
            this.addMenuItemWithPermission(MENU_ITEMS.TRANSMIT, 'message', 'transmit', msg);

            // 收藏 - 所有用户都可以
            this.addMenuItemWithPermission(MENU_ITEMS.SAVE_FAVORITE, 'resource', 'save_favorite', msg);

            // 引用消息 - 需要gmsg_id，仅在聊天组件中显示
            if (msg.gmsg_id && from === "chatComponent") {
                this.addMenuItemWithPermission(MENU_ITEMS.QUOTE, 'message', 'quote', msg);
            }

            // 撤回/删除 - 根据权限决定
            if (this.canWithdrawMessage(msg)) {
                this.menuDatas.push(MENU_ITEMS.WITHDRAW); // 撤回
            } else if (this.canDeleteMessage(msg)) {
                this.menuDatas.push(MENU_ITEMS.DELETE); // 删除
            }

            // 分享微信 - 需要开启微信分享（无权限检查，直接添加）
            if (this.isShowWechat) {
                this.menuDatas.push(MENU_ITEMS.SHARE_WECHAT);
            }

            // 注意：视频剪辑不包含以下功能：
            // - 多选 (MULTI_SELECT)
            // - AI分析 (SEND_TO_ANALYZE)
            // - 图片搜索 (IMAGE_SEARCH)
            // - 重命名 (RENAME)
            // - 编辑审查信息 (EDIT_REVIEW_INFO)
        },

        /**
         * 构建实时视频审查菜单
         */
        buildRealTimeVideoReviewMenu(msg, from) {
            // 对于实时视频审查，需要设置正确的创建者ID用于权限检查
            // 保存原始sender_id
            const originalSenderId = msg.sender_id;

            // 临时设置sender_id为创建者ID，用于权限检查
            if (msg.live_record_data && msg.live_record_data.creator_id) {
                msg.sender_id = msg.live_record_data.creator_id;
            }

            // 转发
            this.addMenuItemWithPermission(MENU_ITEMS.TRANSMIT, 'message', 'transmit', msg);

            // 收藏
            this.addMenuItemWithPermission(MENU_ITEMS.SAVE_FAVORITE, 'resource', 'save_favorite', msg);

            // 引用消息
            if (msg.gmsg_id && from === "chatComponent") {
                this.addMenuItemWithPermission(MENU_ITEMS.QUOTE, 'message', 'quote', msg);
            }

            // 撤回/删除 - 根据权限决定
            if (this.canWithdrawMessage(msg)) {
                this.menuDatas.push(MENU_ITEMS.WITHDRAW); // 撤回
            } else if (this.canDeleteMessage(msg)) {
                this.menuDatas.push(MENU_ITEMS.DELETE); // 删除
            }

            // 编辑审查信息 - 特殊权限检查
            // 注意：这里使用原有的权限检查方式，因为editReviewInfo方法中有特殊的权限验证
            this.addMenuItemWithPermission(MENU_ITEMS.EDIT_REVIEW_INFO, 'resource', 'edit', msg);

            // 分享微信（无权限检查，直接添加）
            if (this.isShowWechat) {
                this.menuDatas.push(MENU_ITEMS.SHARE_WECHAT);
            }

            // 恢复原始sender_id
            msg.sender_id = originalSenderId;
        },

        // ==================== 业务方法 ====================
        showTooltipsMenu(msg, from) {
            if (msg.is_default_image) {
                return;
            }
            this.menuDatas = [];
            this.from = from;
            console.log(msg, from);
            const msg_type = msg.msg_type;
            const msgType = this.systemConfig.msg_type;
            if (msg_type === msgType.AI_ANALYZE) {
                if (msg.ai_analyze && msg.ai_analyze.messages) {
                    this.$root.transmitTempList = msg.ai_analyze.messages || [];
                }
            } else {
                this.$root.transmitTempList = [msg];
            }
            if (this.$parent.$refs.message_text) {
                this.$parent.$refs.message_text.blur();
            }

            // 使用统一的菜单构建方法
            this.buildMenuForMessageType(msg, msg_type, msgType, from);
        },

        /**
         * 根据消息类型构建菜单
         * @param {Object} msg - 消息对象
         * @param {number} msg_type - 消息类型
         * @param {Object} msgType - 消息类型常量
         * @param {string} from - 来源
         */
        buildMenuForMessageType(msg, msg_type, msgType, from) {
            if (msg_type === msgType.Image || msg_type === msgType.Frame || msg_type === msgType.OBAI) {
                this.buildImageVideoMenu(msg, from);
            } else if (msg_type == msgType.TAG || msg_type == msgType.COMMENT) {
                this.buildTagCommentMenu(msg);
            } else if (msg_type === msgType.Video || msg_type === msgType.Cine) {
                this.buildVideoMenu(msg, from);
            } else if (msg_type === msgType.RealTimeVideoReview) {
                this.buildRealTimeVideoReviewMenu(msg, from);
            } else if (msg_type === msgType.VIDEO_CLIP) {
                this.buildVideoClipMenu(msg, from);
            } else if (msg_type === msgType.LIVE_INVITE) {
                this.buildSimpleMenu(msg);
            } else if (msg_type === msgType.IWORKS_PROTOCOL) {
                this.buildSimpleMenu(msg);
            } else if (msg_type === msgType.File) {
                if (!msg.uploading) {
                    this.buildFileMenu(msg);
                }
            } else if (msg_type === msgType.AI_ANALYZE) {
                this.buildAIAnalyzeMenu(msg);
            } else if (msg_type === msgType.EXAM_IMAGES) {
                this.buildExamImagesMenu(msg);
            } else if (msg_type === msgType.Text) {
                this.buildTextMenu(msg, from);
            } else if (msg_type === msgType.Sound) {
                this.buildSoundMenu(msg);
            } else if (msg_type === msgType.HOMEWORK_DETAIL || msgType.HOMEWORK_DETAIL_INDIVIDUAL) {
                this.buildHomeworkMenu(msg);
            }
        },

        /**
         * 构建简单菜单（转发+撤回/删除）
         */
        buildSimpleMenu(msg) {
            // 转发
            this.addMenuItemWithPermission(MENU_ITEMS.TRANSMIT, 'message', 'transmit', msg);

            // 撤回/删除
            if (this.canWithdrawMessage(msg)) {
                this.menuDatas.push(MENU_ITEMS.WITHDRAW);
            } else if (this.canDeleteMessage(msg)) {
                this.menuDatas.push(MENU_ITEMS.DELETE);
            }
        },
        showQuoteMessageMenu(msg, from) {
            this.menuDatas = [MENU_ITEMS.QUOTE];
            this.from = from;
            this.$root.transmitTempList = [msg];
        },
        checkShowSendToAnalyze(msg) {
            let isNotAiAnalyzeServiceType = false;
            if (this.conversation) {
                isNotAiAnalyzeServiceType =
                    this.$store.state.systemConfig.user_service_type.AiAnalyze != this.conversation.service_type;
            }
            if (msg && (msg.been_withdrawn || !msg.resource_id)) {
                return false;
            }
            return (
                this.functionsStatus.breastAI &&
                this.systemConfig.serverInfo.enable_ai_analyze &&
                !this.globalParams.isCE &&
                isNotAiAnalyzeServiceType
            );
        },
        checkShowImageSearch() {
            let enable_ai_search =
                this.$store.state.systemConfig.serverInfo.ai_searcher_server &&
                this.$store.state.systemConfig.serverInfo.ai_searcher_server.enable &&
                !this.globalParams.isCE;
            return this.functionsStatus.breastCases && enable_ai_search;
        },

        showExamItemToolTips(msg) {
            if (msg.hasOwnProperty("examStatus") && msg.examStatus !== 0) {
                //多中心的病例，非未提交不允许删除
            } else {
                if (this.checkExamPermission('delete', msg)) {
                    this.menuDatas.push(MENU_ITEMS.DELETE_EXAM);
                }
            }
        },
        sendToAnalyzeFn() {
            this.menuDatas = [];
            sendToAnalyze();
        },
        handleDeleteExam() {
            console.error(this.currentFile);
            Tool.openMobileDialog({
                message: this.$t('delete_exam_tips'),
                showRejectButton: true,
                confirm: () => {
                    this.deleteExam(this.currentFile);
                },
            });
        },
        deleteExam(file) {
            let params = {
                exam_id: file.exam_id,
            };
            let cid = file.group_id || this.cid;
            window.main_screen.conversation_list[cid].deleteExam(params, (res) => {
                if (res.error_code !== 0) {
                    console.error(res);
                    Toast(this.$t(res.error_msg) || this.$t('delete_case_fail'));
                }
            });
        },
        imageRename() {
            this.imageRenameVisible = true;
            this.menuDatas = [];
        },
        searchInCaseData() {
            let list = this.$root.transmitTempList;
            if (list && list.length > 1) {
                Toast(this.$t('searc_in_case_database_many_picture'));
            } else if (list && list.length == 1) {
                let msg = list[0];
                let islegal = Tool.isLegalForematForSearchImage(msg);
                if (islegal) {
                    let pattern = new RegExp("^data:image.*", "i");
                    msg.realUrl = msg.realUrl && pattern.test(msg.realUrl) ? "" : msg.realUrl;
                    let searchParams = {
                        type: "url", //text,url,file
                        content: msg,
                    };
                    this.$store.commit("caseDatabase/updateSearchParams", searchParams);
                    this.$router.push({ path: `/index/chat_window/${this.cid}/case_database` });
                } else {
                    Toast(this.$t('picture_is_only_jpg_jpeg_bmp_png'));
                }
            } else {
                Toast(this.$t('searc_in_case_database_one_picture'));
            }
        },
        copyText() {
            this.menuDatas = [];
            let text = Tool.replaceHtmlTag(this.currentFile.msg_body);
            console.log(text);
            if (Tool.checkAppClient("IOS") && !Tool.checkAppClient("Browser")) {
                window.CWorkstationCommunicationMng.setClipboard({ str: text });
                Toast(this.$t('text_has_copied'));
            } else {
                Tool.copyToClipboard(text);
                Toast(this.$t('text_has_copied'));
            }
        },
        revocationMessage() {
            // 撤回消息前判断
            let msg = this.$root.transmitTempList[0];
            let msgTimestamp = new Date(msg.send_ts).getTime();
            let nowTimestamp = new Date().getTime();
            if (nowTimestamp - msgTimestamp > this.systemConfig.serverInfo.msg_withdrawal_max_time) {
                // 超出可撤回时间内
                Toast(`${this.$t('exceeded_max_withdrawal')}`);
                return;
            } else {
                // 可撤回
                this.tryToWithDrawMessages(this.conversation.id, [msg]);
            }
            this.menuDatas = [];
        },
        onSelectFavoriteType(item) {
            if (item.key === 0) {
                this.saveFavorite();
            } else {
                this.groupFavoriteDialog = true;
            }
        },
        openFavoriteSelectAction() {
            this.menuDatas = [];
            this.isShowSelectFavoriteType = true;
        },
        setResourceExamInfo() {
            var that = this;
            let list = this.$root.transmitTempList;
            this.menuDatas = [];
            initImportExamImageTempQueue(list, function (err) {
                if (!err) {
                    that.$router.push(that.$route.fullPath + "/exam_manager");
                }
            });
        },
        editReviewInfo() {
            let msg = this.$root.transmitTempList[0];
            service
                .checkActionPermissions({
                    action: "conference.update.recording",
                    businessData: {
                        resource_id: msg.resource_id,
                        group_id: this.cid,
                    },
                })
                .then((res) => {
                    if (res.data.error_code === 0) {
                        if (!res.data.data.hasPermission) {
                            Toast(this.$t('no_permission_operate'));
                            return;
                        }
                        this.$nextTick(() => {
                            this.$refs[`reviewEditDialog`].openEditTargetModal();
                        });
                    } else {
                        Toast(this.$t(res.data.key));
                    }
                })
                .catch((error) => {
                    console.error(error);
                });

            this.menuDatas = [];
        },
        saveFavorite() {
            this.menuDatas = [];
            window.vm.$root.eventBus.$emit("initFavoriteConfirm", this.$root.transmitTempList);
        },
        shareToWechat() {
            this.menuDatas = [];
            this.$root.eventBus.$emit("shareToWechatHandler", this.$root.transmitTempList);
        },
        async submitInsertGroupFavorite(action, done) {
            console.log(action, done);
            if (action === "confirm") {
                try {
                    await this.$refs["chatMessageListGroupFavorite"].insertResourceToCategory(
                        this.$root.transmitTempList[0]
                    );
                    done();
                } catch (error) {
                    done(false);
                }
            } else {
                done();
            }
        },
        multiSelectImage() {
            this.menuDatas = [];
            this.$emit("multiSelectImage");
        },
        deleteChatMessages() {
            this.menuDatas = [];
            let currentFile = this.$root.transmitTempList[0];
            // this.tryToDeleteMessages(this.conversation.id, list);
            let gmsg_id = 0;
            if (this.from === "chatComponent") {
                gmsg_id = currentFile.gmsg_id;
            }
            this.deleteResourceByGroupId(currentFile, gmsg_id);
        },

        transmit() {
            this.menuDatas = [];
            if (this.currentFile.msg_type === this.systemConfig.msg_type.HOMEWORK_DETAIL) {
                service
                    .getanswerSheetByAssignmentID({
                        assignmentID: this.currentFile.assignmentInfo._id,
                    })
                    .then((res) => {
                        if (res.data.error_code === 0) {
                            const assignmentInfo = res.data.data.assignmentInfo;
                            if (assignmentInfo && assignmentInfo.dueTime) {
                                if (Date.now() > assignmentInfo.dueTime) {
                                    // 检查当前用户是否为群主和管理员，只有管理者可以修改作业配置
                                    if (this.checkConversationPermission('edit_settings')) {
                                        Tool.openMobileDialog({
                                            message: this.$t('exam_overdue_creator_tip'),
                                            showRejectButton: true,
                                            confirm: () => {
                                                this.$store.commit("homework/setCurrentPaper", assignmentInfo);
                                                this.$router.push({
                                                    path: `/index/chat_window/${this.cid}/cloud_exam/correcting_exam/student/exam_setting`,
                                                });
                                            },
                                        });
                                    } else {
                                        Toast(this.$t('overdue_assignment_transmit_tip'));
                                    }
                                    return;
                                }
                            }
                        }
                        this.$router.push(`/index/chat_window/${this.cid}/transmit`);
                    });
            } else {
                this.$router.push(`/index/chat_window/${this.cid}/transmit`);
            }
        },
        shareToEmail() {
            let msg = this.$root.transmitTempList[0];
            let resource_ids = [];
            for (let item of msg.resourceList) {
                resource_ids.push(item.id);
            }
            this.$router.push(`/index/chat_window/${this.cid}/share_link/${resource_ids.join()}/2`);
        },
        quoteMessage() {
            // 引用消息
            this.menuDatas = [];
            let msg = this.$root.transmitTempList[0];

            // 检查消息是否有 gmsg_id
            if (!msg || !msg.gmsg_id) {
                return;
            }
            let quoteMessage = {
                msg_body:htmlEscape(msg.original_msg_body),
                gmsg_id:msg.gmsg_id,
                msg_type:msg.msg_type,
                sender_id:msg.sender_id,
                nickname:msg.nickname || '',
                send_ts:msg.send_ts || '',
                resource_id:msg.resource_id || '',
                url:msg.url|| '',
                avatar:msg.avatar|| '',
                thumb:msg.thumb|| '',
                img_id:msg.img_id|| '',
                img_has_gesture_video:msg.img_has_gesture_video|| '',
                file_id:msg.file_id|| '',
                file_name:msg.file_name|| '',
                group_id:msg.group_id|| '',
                original_file_name:msg.original_file_name|| '',
                ultrasound_url: msg.ultrasound_url || '',
                mp4FileUrl: msg.mp4FileUrl || '',
            }
            // 将消息数据传递给事件总线，以便在输入框显示引用
            this.$root.eventBus.$emit("quoteMessage", quoteMessage);
        },
        locateOriginalMessage() {
            this.menuDatas = [];
            let msg = this.$root.transmitTempList[0];
            this.$emit("locateOriginalMessage", msg);
        },
    },
};
</script>
