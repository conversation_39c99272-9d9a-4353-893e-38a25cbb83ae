<template>
    <div>
        <keep-alive :include="keepAliveComponents" v-if="isRouterAlive">
            <router-view></router-view>
        </keep-alive>
        <FunctionalDialog ref="functionalDialog"></FunctionalDialog>
    </div>
</template>
<script>
import { Toast } from 'vant';
import Tool from "@/common/tool.js";
import service from "./service/service";
import { getAppVersion } from "../../common/version";
import FunctionalDialog from './MRComponents/functionalDialog.vue'
export default {
    mixins: [],
    name: "app",
    components: {
        FunctionalDialog
    },
    data() {
        return {
            systemConfig: this.$store.state.systemConfig,
            globalParams: this.$store.state.globalParams,
            isRouterAlive: false,
            keepAliveComponents: ['IndexPage'] // 需要缓存的组件名称
        };
    },
    created() {
        this.isRouterAlive = true
    },
    mounted() {
        var that = this;
        this.$root.functionalDialog = this.$refs.functionalDialog
        //data为?号以后的字符串qv=1.0&act=connect&sn=***&ut=3
        this.$root.eventBus.$off("reloadRouter").$on("reloadRouter", this.reloadRouter);
        this.$root.eventBus.$off("clearKeepAliveCache").$on("clearKeepAliveCache", this.clearKeepAliveCache);
        this.$root.eventBus.$off("ScanQRConnect").$on("ScanQRConnect", (data) => {
            console.log("*********************** ScanQRConnect   ***********************");
            console.log(data);

            //DEBUG_TO_SERVER("ScanQRConnect", JSON.stringify(data));
            if (window.main_screen) {
                window.main_screen.gateway.emit("debug", "ScanQRConnect", JSON.stringify(data));
            }

            if (data && data.act == "con") {
                //先把数据提交了store中去
                that.$store.commit("scanner/initScanner");
                that.$store.commit("scanner/updateScanner", { scanner_str: JSON.stringify(data) });

                that.doConnect(data);
            } else {
                Toast(that.$t('unrecognized_qrcode'));
            }
        });
        this.$root.eventBus.$off("checkIOSUpdate").$on("checkIOSUpdate", this.checkIOSUpdate);
        this.$root.eventBus.$off("authLogin").$on("authLogin", (data) => {
            that.authLogin(data);
        });

        this.$root.eventBus.$off("scanner_notify_mobile_net_type").$on("scanner_notify_mobile_net_type", (data) => {
            var mobile_net_type = data.net_type;
            if (-1 == mobile_net_type) {
                Toast("手机无网络，请先联网");
                return;
            }
            that.doConnect2(mobile_net_type);
        });
    },
    methods: {
        doConnect(data) {
            var that = this;

            var device_id = data.sn;
            var device_type = 0;
            if (data.ut == 3) {
                device_type = that.systemConfig.client_type.AppUltraSyncBox; //7
            } else if (data.ut == 4) {
                device_type = that.systemConfig.client_type.Doppler; //6
            }

            var ssid = "";
            if (data.ssid) {
                ssid = data.ssid;
            }

            var ultraSync_state = parseInt(data.us);
            var device_login_state = this.getLoginState(ultraSync_state);
            var device_register_state = this.getRegisterState(ultraSync_state);
            console.log("ultraSync_state: " + ultraSync_state);
            console.log("device_login_state: " + device_login_state);
            console.log("device_register_state: " + device_register_state);

            that.$store.commit("scanner/updateScanner", {
                device_id: device_id,
                device_type: device_type,
                device_login_state: device_login_state,
                device_register_state: device_register_state,
                ssid: ssid,
            });

            //首先判断移动端是否联网，查询网络类型
            window.CWorkstationCommunicationMng.queryMobileNetType();
        },

        doConnect2(mobile_net_type) {
            //更新返回的手机网络类型，继续进行连接
            var that = this;

            var scanner = window.vm.$store.state.scanner;
            var device_id = scanner.device_id;
            var device_type = scanner.device_type;
            that.DEBUG_TO_SERVER("[qr_login_auth_client] scanner ", scanner);

            //移动端登陆状态
            var mobile_login_state = 0;
            if (
                window.vm.$store.state.user.id != "" &&
                window.vm.$store.state.loadingConfig.networkUnavailable == false
            ) {
                mobile_login_state = 1;
            }
            console.log("mobile_login_state: " + mobile_login_state);
            that.$store.commit("scanner/updateScanner", { mobile_login_state: mobile_login_state });

            if (that.systemConfig.client_type.AppUltraSyncBox == device_type) {
                //盒子端处理
                if (0 == mobile_login_state) {
                    Toast(that.$t('phone_no_login'));
                    that.DEBUG_TO_SERVER("[qr_login_auth_client] 手机未登录，请先登录");
                } else {
                    //that.authLogin({device_id:device_id, device_type:device_type});
                    var controller = window.main_screen.controller;
                    controller.emit(
                        "query_device_login_info",
                        { device_id: device_id, device_type: device_type },
                        function (is_succ, data) {
                            if (is_succ) {
                                //盒子已登录
                                var device_uid = data.uid;
                                var mobile_uid = window.vm.$store.state.user.id;
                                if (device_uid == mobile_uid) {
                                    Toast(that.$t('account_logged_in'));
                                    that.DEBUG_TO_SERVER("[qr_login_auth_client] 此账号已登录");
                                } else {
                                    Tool.openMobileDialog(
                                        {
                                            message: that.$t('switch_account'),
                                            showRejectButton: true,
                                            confirm: () => {
                                                that.authLogin({
                                                    device_id: device_id,
                                                    device_type: device_type,
                                                });
                                            },
                                        }
                                    )
                                }
                            } else {
                                //盒子未登录
                                controller.emit(
                                    "query_device_register_state",
                                    { device_id: device_id },
                                    function (is_succ, data) {
                                        if (is_succ && 1 == data.state) {
                                            //盒子联网
                                            that.authLogin({
                                                device_id: device_id,
                                                device_type: device_type,
                                            });
                                        } else {
                                            Toast(that.$t('box_not_found'));
                                            that.DEBUG_TO_SERVER("[qr_login_auth_client] 找不到盒子");
                                        }
                                    }
                                );
                            }
                        }
                    );
                }
            } else if (that.systemConfig.client_type.Doppler == device_type) {
                //doppler端处理
                //ultraSync_state状态 -> device_login_state和device_register_state
                var scanner = window.vm.$store.state.scanner;
                var device_login_state = scanner.device_login_state;
                var device_register_state = scanner.device_register_state;
                var scanner_str = scanner.scanner_str;
                // var ssid = scanner.ssid
                console.log("scanner_str " + scanner_str);
                that.DEBUG_TO_SERVER("[qr_login_auth_client] scanner_str ", scanner_str);

                //1.设备联网，未登录，手机登录
                if (1 == device_register_state && 0 == device_login_state && 1 == mobile_login_state) {
                    console.log("*********** 1.设备联网，未登录，手机登录 ***************");
                    that.DEBUG_TO_SERVER(
                        "[qr_login_auth_client] *********** 1.设备联网，未登录，手机登录 ***************"
                    );

                    //查询是否注册到此服务器
                    console.log("send qr_login_auth ", {
                        device_id: device_id,
                        device_type: device_type,
                    });
                    var controller = window.main_screen.controller;
                    controller.emit("query_device_register_state", { device_id: device_id }, function (is_succ, data) {
                        if (is_succ && 1 == data.state) {
                            that.authLogin({ device_id: device_id, device_type: device_type }, function () {
                                // window.CWorkstationCommunicationMng.notifyUltrasoundMachineInfo(
                                //     scanner_str
                                // )
                            });
                        } else {
                            console.log("设备未注册到此服务器，不在同一网络");
                            that.DEBUG_TO_SERVER("[qr_login_auth_client] 设备未注册到此服务器，不在同一网络");
                            //Toast("设备未注册到此服务器，不在同一网络");
                            // setTimeout(function () {
                            //     window.CWorkstationCommunicationMng.notifyUltrasoundMachineInfo(
                            //         scanner_str
                            //     )
                            // }, 3000)
                        }
                    });
                }

                //2.设备联网，未登录，手机未登录
                // if (
                //     1 == device_register_state &&
                //         0 == device_login_state &&
                //         0 == mobile_login_state
                // ) {
                //     console.log('************* 2.设备联网，未登录，手机未登录 **********')
                //     that.DEBUG_TO_SERVER(
                //         '[qr_login_auth_client] ************* 2.设备联网，未登录，手机未登录 **********'
                //     )
                //     window.CWorkstationCommunicationMng.notifyUltrasoundMachineInfo(scanner_str)
                // }

                //3.设备无联网，手机登录
                if (0 == device_register_state && 1 == mobile_login_state) {
                    console.log("***************** 3.设备无联网，手机登录 ***************");
                    that.DEBUG_TO_SERVER(
                        "[qr_login_auth_client] ***************** 3.设备无联网，手机登录 ***************"
                    );
                    //int, -1 无网络， 0 移动网络，1  WIFI
                    if (1 == mobile_net_type) {
                        //WIFI
                        Tool.openMobileDialog(
                            {
                                message: that.$t('device_has_no_network'),
                            }
                        )
                    } else if (0 == mobile_net_type) {
                        //移动网络
                        Tool.openMobileDialog(
                            {
                                message: that.$t('device_has_no_network'),
                            }
                        )
                    } else {
                        Toast(that.$t('incorrect_network'));
                        that.DEBUG_TO_SERVER("[qr_login_auth_client] 网络类型不正确，无网络");
                    }
                }
                //4.设备无联网，手机未登录
                if (0 == device_register_state && 0 == mobile_login_state) {
                    console.log("****************  4.设备无联网，手机未登录 *************");
                    that.DEBUG_TO_SERVER(
                        "[qr_login_auth_client] ****************  4.设备无联网，手机未登录 *************"
                    );
                    //int, -1 无网络， 0 移动网络，1  WIFI
                    if (1 == mobile_net_type) {
                        //WIFI
                        Tool.openMobileDialog(
                            {
                                message: that.$t('device_has_no_network'),
                            }
                        )
                    } else if (0 == mobile_net_type) {
                        //移动网络
                        Tool.openMobileDialog(
                            {
                                message: that.$t('device_has_no_network'),
                            }
                        )
                    } else {
                        Toast(that.$t('incorrect_network'));
                        that.DEBUG_TO_SERVER("[qr_login_auth_client] 网络类型不正确，无网络");
                    }
                }

                //5.设备联网，已登录，手机已登录
                if (1 == device_register_state && 1 == device_login_state && 1 == mobile_login_state) {
                    console.log("*************** 5.设备联网，已登录，手机已登录 **********");
                    that.DEBUG_TO_SERVER(
                        "[qr_login_auth_client] *************** 5.设备联网，已登录，手机已登录 **********"
                    );
                    //查询设备登录的userid
                    var controller = window.main_screen.controller;
                    controller.emit(
                        "query_device_login_info",
                        { device_id: device_id, device_type: device_type },
                        function (is_succ, data) {
                            if (is_succ) {
                                var device_uid = data.uid;
                                var mobile_uid = window.vm.$store.state.user.id;
                                if (device_uid == mobile_uid) {
                                    Toast(that.$t('account_logged_in'));
                                    that.DEBUG_TO_SERVER("[qr_login_auth_client] 此账号已登录");
                                    // setTimeout(function () {
                                    //     window.CWorkstationCommunicationMng.notifyUltrasoundMachineInfo(
                                    //         scanner_str
                                    //     )
                                    // }, 3000)
                                } else {
                                    Tool.openMobileDialog(
                                        {
                                            message: that.$t('switch_account'),
                                            showRejectButton: true,
                                            confirm: () => {
                                                that.authLogin({
                                                    device_id: device_id,
                                                    device_type: device_type,
                                                });
                                            },
                                        }
                                    )
                                }
                            } else {
                                Toast(that.$t('device_not_logged_server'));
                                that.DEBUG_TO_SERVER("[qr_login_auth_client] 设备未登录到此服务器，不在同一网络");
                                // setTimeout(function () {
                                //     window.CWorkstationCommunicationMng.notifyUltrasoundMachineInfo(
                                //         scanner_str
                                //     )
                                // }, 3000)
                            }
                        }
                    );
                }
                //6.设备联网，已登录，手机未登录
                // if (
                //     1 == device_register_state &&
                //         1 == device_login_state &&
                //         0 == mobile_login_state
                // ) {
                //     console.log('************** 6.设备联网，已登录，手机未登录 ************')
                //     that.DEBUG_TO_SERVER(
                //         '[qr_login_auth_client] ************** 6.设备联网，已登录，手机未登录 ************'
                //     )
                //     window.CWorkstationCommunicationMng.notifyUltrasoundMachineInfo(scanner_str)
                // }
            } else {
                Toast(that.$t('unrecognized_device_type'));
                that.DEBUG_TO_SERVER("[qr_login_auth_client] 不识别的设备类型");
            }
        },

        authLogin(param, fn) {
            var that = this;
            console.log("authLogin");
            console.log(param);
            that.DEBUG_TO_SERVER("[qr_login_auth_client] authLogin ", param);
            var device_id = param.device_id;
            var device_type = param.device_type;

            var user = window.vm.$store.state.user;
            var uid = user.id;
            var name = user.username;

            var controller = window.main_screen.controller;
            var data = {
                uid: uid,
                name: name,
                device_id: device_id,
                device_type: device_type,
            };
            console.log("send qr_login_auth ", data);
            controller.emit("qr_login_auth", data, function (is_succ, data) {
                if (is_succ) {
                    Toast(that.$t('auth_ok'));
                } else {
                    Toast(that.$t('auth_fail'));
                }
                if (fn) {
                    setTimeout(function () {
                        fn();
                    }, 3000);
                }
            });
        },
        getLoginState(ultraSync_state) {
            var result = ultraSync_state & this.systemConfig.UltraSync_State.UltraSync_Login;
            if (result == 0) {
                return 0;
            } else {
                return 1;
            }
        },
        getRegisterState(ultraSync_state) {
            var result = ultraSync_state & this.systemConfig.UltraSync_State.UltraSync_registered;
            if (result == 0) {
                return 0;
            } else {
                return 1;
            }
        },
        DEBUG_TO_SERVER(msg, data) {
            if (window.main_screen) {
                window.main_screen.gateway.emit("debug", msg, data);
            }
        },
        async checkIOSUpdate() {
            if (Tool.checkAppClient("App") && Tool.checkAppClient("IOS") && !Tool.checkAppClient("TEAir")) {
                const res = await getAppVersion();
                let versions = res.data.app_desc.version.split(".");
                // let versions=window.AppVersion.app_desc.version.split('.');
                // let versions="2.06.666".split('.')
                let MajorVersion = versions[0] + "." + versions[1];
                let MinorVersion = versions[2];
                service.upgradeIOSInfo({ MajorVersion, MinorVersion }).then((res) => {
                    if (res.data.error_code == 0) {
                        this.upgradeIOS(res.data.data, MinorVersion, MajorVersion);
                    }
                });
            }
        },
        upgradeIOS(data, MinorVersion, MajorVersion) {
            let force = false;
            let that = this;
            if (data.Action == 1) {
                force = true;
            } else if (data.Action == 2) {
                let ignoreUpgradeVersion = window.localStorage.getItem("ignoreUpgradeVersion");
                if (ignoreUpgradeVersion !== null) {
                    if (ignoreUpgradeVersion === `${MajorVersion}${MinorVersion}`) {
                        return;
                    }
                }
            } else {
                Toast(this.$t('no_new_version'));
                return;
            }
            var info = `${that.$t('cur_version')} ${MajorVersion}.${MinorVersion} <br/>${that.$t('new_version')} ${data.MajorVersion}.${data.MinorVersion}<br/>${that.$t('confirm_update_to_new_version')}`;

            Tool.openMobileDialog(
                {
                    message: info,
                    showRejectButton: !force,
                    showCancelButton: !force,
                    forbiddenClose: force,
                    closeOnClickOverlay: false,
                    closeOnPopstate: false,
                    confirm: () => {
                        window.CWorkstationCommunicationMng.forceUpdateApp({
                            apk_download_url: data.DownloadUrl,
                        });
                    },
                    reject: () => {
                        window.localStorage.setItem("ignoreUpgradeVersion", `${MajorVersion}${MinorVersion}`);
                    },
                    cancel: () => {
                        window.localStorage.setItem("ignoreUpgradeVersion", `${MajorVersion}${MinorVersion}`);
                    }
                }
            )
        },
        reloadRouter() {
            this.$router.replace("/login");
            this.clearKeepAliveCache();
        },
        clearKeepAliveCache() {
            console.log('clearKeepAliveCache')
            // 清除keep-alive缓存的方法：
            // 1. 先移除MainPage从缓存列表
            this.keepAliveComponents = [];

            // 2. 强制重新渲染keep-alive组件
            this.isRouterAlive = false;
            this.$nextTick(() => {
                // 3. 重新启用router-view和keep-alive
                this.isRouterAlive = true;
                // 4. 重新设置需要缓存的组件
                this.keepAliveComponents = ['IndexPage'];
            });
        },
    },
};
</script>
<style lang="scss">
* {
    touch-action: auto; //这个是重点如果不加新版谷歌会忽略掉touch方法
    word-break: break-word;
}

*:not(input, textarea) {
    //兼容IOS下 长按会弹出默认交互问题
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    /* Disable selection/Copy of UIWebView */
    touch-callout: none;
}

.prevent_ios_longtouch {
    pointer-events: none;
}

.mr-nav-bar {
    .more {
        line-height: 2.95rem;
        height: 2.95rem;

        .svg_circle_plus {
            top: 1rem;
        }

        i {
            line-height: 1;
            position: absolute;
            top: 1rem;
        }
    }

    .transfer_panel {
        .svg_icon_transfer {
            font-size: 1rem;
        }
    }
}

.index_header {
    align-items: center;

    .svg_app_title {
        top: -0.15rem;
    }
}

.page-tab-container {
    // height: calc(100% - 6.35rem);
}

#__vconsole {
    user-select: none;
    -webkit-user-select: none;
    touch-callout: none;
    -webkit-touch-callout: none;
}

.van-switch {
    width: 1.6rem !important;
    height: 1rem !important;

    .van-switch__node {
        top: -0.15rem !important;
        left: -0.4rem !important;
        width: 1.2rem !important;
        height: 1.2rem !important;
    }

    &.van-switch--on .van-switch__node {
        transform: translateX(.8em);
    }
}

.more_panel {
    position: absolute;
    width: 1.75rem;
    height: 100%;
    right: 0;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;

    .svg_icon_more {
        width: 0.95rem;
        height: 0.2rem;
        font-size: 0.24rem;
        color: #fff;
    }
}

.textEllipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.van-button--primary {
    background-color: #00c59d !important;
    border: 1px solid #00c59d !important;
}

.van-button--plain {
    background-color: #fff !important;
}

.van-button--plain.van-button--primary {
    color: #00c59d !important;
}

.network_unavailable {
    line-height: 1.4rem;
    font-size: 0.7rem;
    color: #333;
    background-color: #f9d5d5;
    padding: 0.3rem;

    &>i {
        font-size: 1rem;
        color: #ff6759;
    }
}

.van-toast .van-toast__text {
    font-size: .8rem;
}

.van-loading__circular circle {
    stroke-width: .15rem;
}

.icon-png {
    height: 2.5rem;
    width: 2.5rem;
}

// 修复van-picker工具栏按钮换行问题
.van-picker {

    // 修复van-picker标题显示，最多两行，超过显示省略号
    .van-picker__title {
        font-size: 0.73rem !important; // 16px = 0.73rem
        font-weight: 500 !important;
        line-height: 1.4 !important;
        color: #323233 !important;
        text-align: center !important;
        padding: 0 0.91rem !important; // 0 20px
        max-height: 2.05rem !important; // 约两行的高度 (0.73rem * 1.4 * 2 ≈ 2.05rem)
        overflow: hidden !important;
        display: -webkit-box !important;
        -webkit-box-orient: vertical !important;
        -webkit-line-clamp: 2 !important;
        line-clamp: 2 !important; // 标准属性，用于兼容性
        word-break: break-word !important;
        white-space: normal !important;
    }

    .van-picker__cancel,
    .van-picker__confirm {
        font-size: 0.73rem !important;
        line-height: 1.2 !important;
        padding: 0 1rem !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        min-width: 3.18rem !important;
        text-align: center !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    // 修复van-picker标题显示，最多两行，超过显示省略号
    .van-picker__title {
        font-size: 0.73rem !important;
        font-weight: 500 !important;
        line-height: 1.4 !important;
        color: #323233 !important;
        text-align: center !important;
        padding: 0 0.5rem !important;
        max-height: 2.05rem !important; // 约两行的高度 (0.73rem * 1.4 * 2 ≈ 2.05rem)
        overflow: hidden !important;
        display: -webkit-box !important;
        -webkit-box-orient: vertical !important;
        -webkit-line-clamp: 2 !important;
        line-clamp: 2 !important; // 标准属性，用于兼容性
        word-break: break-word !important;
        white-space: normal !important;
    }

    .van-picker-column__item {
        div {
            overflow: hidden !important;
            display: -webkit-box !important;
            -webkit-box-orient: vertical !important;
            -webkit-line-clamp: 2 !important;
            line-clamp: 2 !important;
            white-space: normal !important;
        }
    }
}
</style>
