<template>
	<div class="tooltips-menu" v-if="menuDatas.length>0">
        <div class="first-wrap" v-if="firstArr.length>0">
            <van-button
                v-for="(itemKey, index) in firstArr"
                :key="itemKey"
                class="tooltips-menu-item"
                :class="{ 'has-separator': index < firstArr.length - 1 }"
                type="default"
                @click="clickItem(getItemByKey(itemKey))"
                :style="getButtonStyle($t(getItemByKey(itemKey).name))">
                <div class="button-text-wrapper">{{ $t(getItemByKey(itemKey).name) }}</div>
            </van-button>
        </div>
        <div class="second-wrap" v-if="secondArr.length>0">
            <van-button
                v-for="(itemKey, index) in secondArr"
                :key="itemKey"
                class="tooltips-menu-item"
                :class="{ 'has-separator': index < secondArr.length - 1 }"
                type="default"
                @click="clickItem(getItemByKey(itemKey))"
                :style="getButtonStyle($t(getItemByKey(itemKey).name))">
                <div class="button-text-wrapper">{{ $t(getItemByKey(itemKey).name) }}</div>
            </van-button>
        </div>
        <div class="third-wrap" v-if="thirdArr.length>0">
            <van-button
                v-for="(itemKey, index) in thirdArr"
                :key="itemKey"
                class="tooltips-menu-item"
                :class="{ 'has-separator': index < thirdArr.length - 1 }"
                type="default"
                @click="clickItem(getItemByKey(itemKey))"
                :style="getButtonStyle($t(getItemByKey(itemKey).name))">
                <div class="button-text-wrapper">{{ $t(getItemByKey(itemKey).name) }}</div>
            </van-button>
        </div>
	</div>
</template>
<script>
import { Button } from 'vant'
import base from '../lib/base'
import {getLanguage} from '@/common/i18n'
import { MENU_ITEMS, MENU_ITEM_CONFIG } from '../lib/constants.js'
export default {
    components:{
        VanButton: Button,
    },
    mixins: [base],
    data(){
        return {
            transfer: MENU_ITEM_CONFIG,
            firstArr:[],
            secondArr:[],
            thirdArr:[],
        }
    },
    props:{
        menuDatas:{
            type:Array,
            default:()=>{
                return []
            }
        },
    },
    created(){
        this.firstArr = []
        this.secondArr = []
        this.thirdArr = []
        if(getLanguage() ==='CN'){
            this.menuDatas.forEach((item,index)=>{
                if(index<4){
                    this.firstArr.push(item)
                }else if(index>=4&&index<8){
                    this.secondArr.push(item)
                }else if(index>=8){
                    this.thirdArr.push(item)
                }
            })
        }else{
            this.menuDatas.forEach((item,index)=>{
                if(index<3){
                    this.firstArr.push(item)
                }else if(index>=3&&index<6){
                    this.secondArr.push(item)
                }else if(index>=6){
                    this.thirdArr.push(item)
                }
            })
        }



    },
    beforeDestroy(){
        this.firstArr = []
        this.secondArr = []
        this.thirdArr = []
    },
    methods:{
        getItemByKey(key) {
            return this.transfer.find(item => item.key === key);
        },
        clickItem(item){
            // 使用配置中的事件名称，避免魔法值
            if (item.event) {
                this.$emit(item.event);
            } else {
                console.warn('Menu item has no event configured:', item);
            }
        },
        getButtonStyle(text) {
            // 根据文本长度动态计算按钮宽度，确保单词不会被拆分
            const textLength = text.length;
            const isEnglish = /^[a-zA-Z\s]+$/.test(text);

            // 更宽松的宽度设置，避免单词被拆分
            let minWidth = '4rem';
            let maxWidth = '7rem';

            if (isEnglish) {
                // 英文文本需要更多空间，特别是避免单词拆分
                if (textLength > 15) {
                    minWidth = '5rem';
                    maxWidth = '8rem';
                } else if (textLength > 10) {
                    minWidth = '4.5rem';
                    maxWidth = '7.5rem';
                } else if (textLength > 6) {
                    minWidth = '4rem';
                    maxWidth = '6.5rem';
                } else {
                    minWidth = '3.5rem';
                    maxWidth = '5.5rem';
                }
            } else {
                // 中文文本相对紧凑，但也要给足够空间
                if (textLength > 6) {
                    minWidth = '4.5rem';
                    maxWidth = '6rem';
                } else if (textLength > 4) {
                    minWidth = '4rem';
                    maxWidth = '5.5rem';
                } else {
                    minWidth = '3.5rem';
                    maxWidth = '5rem';
                }
            }

            return {
                minWidth,
                maxWidth
            };
        }
    }
}
</script>
<style lang="scss">
.tooltips-menu{
    background-color: #060607;
    position: relative;
    flex: 1;
    border-radius: 5px;
    padding: 0 0;
    display: flex;
    overflow: hidden;
    flex-direction: column;
    align-items: flex-start;
    max-width: min(22rem, calc(100vw - 2rem));
    width: fit-content;
    .tooltips-menu-item{
        padding: 0.3rem 0.2rem !important;
        font-size: 0.7rem;
        background-color: #060607;
        color: #fff;
        border: 0;
        text-overflow: unset;
        flex: 1;
        height: auto;
        min-height: 2rem;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        text-align: center !important;
        box-sizing: border-box !important;
    }

    // 强制覆盖Vant按钮的所有可能样式
    .tooltips-menu-item.van-button {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        text-align: center !important;
        padding: 0.3rem 0.2rem !important;

        .van-button__text{
            white-space: pre-wrap !important;
            word-break: break-word !important;
            line-height: 1.2 !important;
            width: 100% !important;
            text-align: center !important;
            display: block !important;
            min-height: inherit !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        .button-text-wrapper {
            width: 100% !important;
            text-align: center !important;
            white-space: normal !important;
            word-break: keep-all !important;
            overflow-wrap: break-word !important;
            line-height: 1.2 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            min-height: inherit !important;
            hyphens: none !important;
        }
    }
}

// 额外的样式重置，确保完全居中
.tooltips-menu .van-button {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;

    * {
        text-align: center !important;
    }

    .button-text-wrapper {
        width: 100% !important;
        text-align: center !important;
        white-space: normal !important;
        word-break: keep-all !important;
        overflow-wrap: break-word !important;
        line-height: 1.2 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        hyphens: none !important;
    }
}

.first-wrap{
    display: flex;
    width: 100%;
    gap: 0;
    align-items: stretch;
}
.second-wrap,.third-wrap{
    border-top: 1px solid #757575;
    width: 100%;
    display: flex;
    gap: 0;
    align-items: stretch;
}

/* 为有分隔符的按钮添加右边框 */
.tooltips-menu-item.has-separator {
    border-right: 1px solid #757575;
}
</style>
