<template>
<div>
	<CommonDialog
      class="invite_registration"
      :title="$t('invite_registration')"
      :show.sync="visible"
      :close-on-click-modal="false"
      width="40%"
      :modal="false"
      @closed="handleClose"
      @opened="handleOpened"
      :footShow = "false"
      >
        <div class="referral_code_body">
            <div class="download_app_tip">
                <p class="download_app_tip_text">{{$t('download_app_tip')}}</p>
                <div class="qrcode_container">
                  <!-- 使用固定高度的容器，避免高度跳跃 -->
                  <div class="qrcode_wrapper">
                    <div class="qrcode" ref="qrCode" v-show="!isLoading && referral_code">
                      <!-- QR码将在这里生成 -->
                    </div>
                    <div v-show="isLoading" class="qrcode_placeholder">
                      <i class="el-icon-loading"></i>
                      <span class="loading_text"></span>
                    </div>
                  </div>
                </div>
            </div>
            <div class="referral_code_content">
                <span class="referral_code_content_left">{{$t('referral_code')}}:</span>
                <span class="referral_code_content_right">{{referral_code}}</span>
            </div>
            <div class="referral_code_tip">
                <span>{{$t('referral_code_tip')}}</span>
                <span v-show="isShowReferralCodeTipExpire" class="referral_code_tip_expire">{{referral_code_tip_expire}}</span>
            </div>
            <div class="referral_code_tool">
                <el-button
                  :disabled="!enable_generate_referral_code || isLoading"
                  @click="generateReferralCodeDebounced"
                  type="primary"
                  size="middle"
                  class="generate_btn">
                  <i v-if="isLoading" class="el-icon-loading"></i>
                  {{isLoading ? '' : $t('referral_code_generate')}}
                </el-button>
            </div>
        </div>
    </CommonDialog>
</div>

</template>
<script>
import base from '../lib/base'
import {formatString} from '../lib/common_base'
import Tool from '@/common/tool.js'
import CommonDialog from "../MRComponents/commonDialog.vue"; //3rd change

export default {
    mixins: [base],
    name: 'InviteRegistration',
    components: {CommonDialog},
    props: {
        value: {
            type: Boolean,
            default: false
        }
    },
    data(){
        return {
            PROJECT_NOV:process.env.VUE_APP_PROJECT_NOV,
            referral_code:"",
            referral_code_tip_expire:"",
            enable_generate_referral_code:true,
            isLoading: false,
            qrCodeInstance: null,
            // download_app_qr_image:""
        }
    },
    computed:{
        visible: {
            get() {
                return this.value;
            },
            set(val) {
                this.$emit('input', val);
            }
        },
        isShowReferralCodeTipExpire(){
            return 0 < this.referral_code.length;
        }
    },
    mounted(){
        // 移除自动生成，改为在弹窗打开时生成
    },
    methods:{
        // 弹窗打开后的回调
        handleOpened() {
            // 延迟生成邀请码，避免阻塞弹窗动画
            this.$nextTick(() => {
                setTimeout(() => {
                    this.generateReferralCode();
                }, 100);
            });
        },

        // 防抖处理的生成邀请码方法
        generateReferralCodeDebounced: Tool.debounce(function() {
            this.generateReferralCode();
        }, 300),

        generateReferralCode() {
            if (this.isLoading) {
                return; // 防止重复调用
            }

            var that = this;
            that.isLoading = true;
            that.enable_generate_referral_code = false;

            var time_out = setTimeout(function () {
                that.generateReferralCodeError("time_out");
                that.enable_generate_referral_code = true;
                that.isLoading = false;
            }, 10000);

            window.main_screen.controller.emit("request_generate_referral_code",{},function (err, result){
                console.log("callback in request_generate_referral_code", err, result);
                clearTimeout(time_out);
                that.enable_generate_referral_code = true;
                that.isLoading = false;

                if (err) {
                    //失败
                    var error_code = "unknown_err";
                    if ("none_referral_code" == err) {
                        error_code = err;
                    } else if ("database_err" == err) {
                        error_code = err;
                    }
                    that.generateReferralCodeError(error_code);
                } else {
                    //成功
                    if (result && result.referral_code && result.creation_ts) {
                        that.referral_code = result ? result.referral_code : "";
                        that.referral_code_tip_expire = formatString(that.$t('referral_code_tip_expire'), {
                            1:result.creation_ts
                        });
                        // 延迟生成QR码，确保DOM已更新
                        that.$nextTick(() => {
                            setTimeout(() => {
                                that.initQrCode();
                            }, 50);
                        });
                    } else {
                        that.generateReferralCodeError("unknown_err");
                    }
                }
            })
        },
        generateReferralCodeError: function (error_code) {
            this.referral_code = "";
            this.referral_code_tip_expire = "";
            this.isLoading = false;
            this.$message.error(this.$t('referral_code_generate_err')[error_code]);
        },

        initQrCode(){
            if (!this.$refs.qrCode || !this.referral_code) {
                return;
            }

            try {
                let user = this.$store.state.user;

                // 清理之前的QR码实例
                if (this.qrCodeInstance) {
                    this.$refs.qrCode.innerHTML = '';
                    this.qrCodeInstance = null;
                }

                let timestamp = Date.parse(new Date());
                let serverUrl = this.systemConfig.server_type.protocol + this.systemConfig.server_type.host + this.systemConfig.server_type.port + '/';
                let url = Tool.transferLocationToCe(serverUrl + `activity/activity.html#/qr_install_app?act=add_friend&uid=${user.uid}&timestamp=${timestamp}&uname=${user.username}&referral_code=${this.referral_code}`);

                // 创建QR码实例并保存引用
                this.qrCodeInstance = new window.QRCode(this.$refs.qrCode, {
                    text: url,
                    width: 142,
                    height: 142,
                    colorDark: "#000000",
                    colorLight: "#ffffff",
                    correctLevel: window.QRCode.CorrectLevel.M
                });
            } catch (error) {
                console.error('生成QR码失败:', error);
                this.$message.error('生成二维码失败，请重试');
            }
        },

        handleClose(){
            // 清理QR码实例
            if (this.qrCodeInstance) {
                this.qrCodeInstance = null;
            }

            // 重置状态
            this.isLoading = false;
            this.referral_code = "";
            this.referral_code_tip_expire = "";
            this.enable_generate_referral_code = true;

            // 关闭弹窗
            this.visible = false;
        }
    }
}
</script>
<style lang="scss">
.invite_registration{
    .el-dialog__body{
        display:flex;
        flex-direction: column;
        padding: 32px 48px 28px 48px;
        background: #f4f6fa;
        border-radius: 16px;
        box-shadow: 0 6px 32px rgba(0,0,0,0.08);

        .referral_code_body{
            font-size:16px;
            overflow:auto;
            max-width: 420px;
            margin: 0 auto;
            min-height: 480px; // 设置最小高度，保持布局稳定
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .download_app_tip {
                margin-top: 0;
                text-align: center;

                .download_app_tip_text {
                    margin-bottom: 18px;
                    color: #333;
                    font-size: 16px;
                    font-weight: 500;
                    letter-spacing: 1px;
                }

                .qrcode_container {
                    display: flex;
                    justify-content: center;
                    margin-bottom: 18px;

                    .qrcode_wrapper {
                        // 固定容器尺寸，避免高度跳跃
                        width: 166px;
                        height: 166px;
                        position: relative;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }

                    .qrcode {
                        padding: 12px;
                        background: #fff;
                        border-radius: 12px;
                        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.10);
                        border: 1.5px solid #e3e6ed;
                        transition: all 0.3s ease;
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        canvas {
                            display: block;
                        }
                    }

                    .qrcode_placeholder {
                        width: 100%;
                        height: 100%;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        background: #f8f9fa;
                        border-radius: 12px;
                        border: 1.5px dashed #e3e6ed;
                        position: absolute;
                        top: 0;
                        left: 0;
                        transition: all 0.3s ease;

                        i {
                            font-size: 24px;
                            color: #1a73e8;
                            animation: rotate 1s linear infinite;
                            margin-bottom: 8px;
                        }

                        .loading_text {
                            font-size: 12px;
                            color: #666;
                            font-weight: 500;
                        }
                    }
                }
            }

            .referral_code_content{
                text-align: center;
                margin: 28px 0 18px 0;
                padding: 24px 0 18px 0;
                background: linear-gradient(90deg, #fafdff 0%, #f3f7fa 100%);
                border-radius: 12px;
                border: 1.5px dashed #b5c9e2;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                box-shadow: 0 2px 8px rgba(0,0,0,0.04);
                min-height: 100px; // 设置最小高度，避免内容变化时的跳跃

                .referral_code_content_left{
                    color: #2d3a4b;
                    font-weight: 600;
                    margin-bottom: 8px;
                    font-size: 15px;
                    letter-spacing: 1px;
                }

                .referral_code_content_right{
                    font-size: 38px;
                    font-weight: 800;
                    color: #1a73e8;
                    letter-spacing: 4px;
                    font-family: 'Courier New', monospace;
                    text-align: center;
                    background: linear-gradient(90deg, #e3f0ff 0%, #fafdff 100%);
                    border-radius: 6px;
                    padding: 6px 18px;
                    margin-top: 2px;
                    transition: all 0.3s ease; // 添加过渡动画
                    min-height: 50px; // 确保有最小高度
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }

            .referral_code_tip{
                margin-top: 8px;
                text-align: center;
                color: #6c757d;
                font-size: 14px;
                line-height: 1.7;
                letter-spacing: 0.5px;
                min-height: 60px; // 设置最小高度，为过期提示预留空间
                display: flex;
                flex-direction: column;
                justify-content: flex-start;

                span {
                    display: block;
                    text-align: center;
                    transition: all 0.3s ease; // 添加过渡动画
                }

                .referral_code_tip_expire {
                    display: block;
                    margin-top: 8px;
                    color: #e53935;
                    font-weight: 600;
                    text-align: center;
                    font-size: 15px;
                    opacity: 1;
                    transform: translateY(0);
                    transition: all 0.3s ease;
                }
            }

            .referral_code_tool{
                margin-top: 32px;
                text-align: center;
                min-height: 70px; // 设置最小高度，确保按钮区域稳定
                display: flex;
                align-items: center;
                justify-content: center;

                .generate_btn {
                    padding: 14px 38px;
                    font-size: 18px;
                    border-radius: 32px;
                    background: linear-gradient(90deg, #4f8cff 0%, #1a73e8 100%);
                    color: #fff;
                    font-weight: 700;
                    border: none;
                    box-shadow: 0 2px 8px rgba(26,115,232,0.10);
                    transition: all 0.2s cubic-bezier(.4,0,.2,1);
                    min-width: 160px; // 设置最小宽度，避免文字变化时的宽度跳跃
                    position: relative;

                    i {
                        margin-right: 6px;
                        animation: rotate 1s linear infinite;
                    }

                    &:hover:not(:disabled) {
                        transform: translateY(-2px) scale(1.04);
                        box-shadow: 0 6px 18px rgba(26,115,232,0.18);
                        background: linear-gradient(90deg, #1a73e8 0%, #4f8cff 100%);
                    }

                    &:disabled {
                        opacity: 0.6;
                        cursor: not-allowed;
                        background: #b5c9e2;
                        color: #fff;
                        transform: none;
                    }
                }
            }
        }
    }
}

// 添加旋转动画
@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

// 邀请注册弹窗特定样式优化
.invite_registration {
    // 继承 CommonDialog 的动画效果，这里只需要特定的样式调整
    .el-dialog {
        // 可以在这里添加特定于邀请注册弹窗的样式
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    }
}
</style>
