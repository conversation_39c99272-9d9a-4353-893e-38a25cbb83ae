import CMainScreenController from "@/common/socket/CMainScreenController"
import CMainScreenGateway from "@/common/socket/CMainScreenGateway"
import CConversation from "./CConversation"
import CAiAnalyze from "./CAiAnalyze"
import CFileTransferAssistant from "./CFileTransferAssistant"
import CCentralStation from "./CCentralStation"
import CFeedbackQuestionAssistant from "./CFeedbackQuestionAssistant"
import Tool from '@/common/tool.js';
import ServiceConfig from '@/common/ServiceConfig.js';
import CMonitorWallPush from "@/common/CLiveConferenceNative/CMonitorWallPush"
import { Toast } from 'vant';
import {cloneDeep} from 'lodash'
import { Logger } from "@/common/console";
import i18n from '@/common/i18n';
import permissionManager from '@/common/permission/PermissionManager.js';
import { GROUP_ROLE } from '@/common/permission/constant.js';
function CMainScreen(option){
    console.log("[event] CMainScreen.construct",Tool.triggerTime());
    this.uid=option.uid;
    this.client_type=option.client_type;
    this.client_uuid=option.client_uuid;
    this.url=option.url;
    this.conversation_list = {};
    this.localFriendList=[];
    this.gatewayFriendList=[]
    this.isGetLocalFriend=false;
    this.isGetGatewayFriend=false;
    this.localGroupList=[];
    this.gatewayGroupList=[];
    this.isGetLocalGroup=false;
    this.isGetGatewayGroup=false;
    this.localChatList=[];
    this.gatewayChatList=[];
    this.isGetLocalChatList=false;
    this.isGetGatewayChatList=false;
    this.localLastMessages=[];
    this.gatewayLastMessages=[]
    this.isGetLocalLastMessage=false;
    this.isGetGatewayLastMessage=false;

    // 使用全局权限管理器
    this.permissionManager = permissionManager;

    this.controller=new CMainScreenController({
        uid:this.uid
    });
    this.gateway=new CMainScreenGateway({
        uid:this.uid,
        url:this.url
    });
    this.initController();
}
CMainScreen.prototype.initMonitorWallPush=function(){
    var that=this;
    that.CMonitorWallPush = new CMonitorWallPush({
        uid: that.uid,
        client_type: that.client_type,
        client_uuid: that.client_uuid,
        controller:that.controller,
        gateway:that.gateway,
    });
    const oldInstance = window.CMonitorWallPush;
    const newInstance = that.CMonitorWallPush;
    if(oldInstance){
        Tool.rebindEvents(oldInstance, newInstance);
    }
    window.CMonitorWallPush = newInstance;

};
CMainScreen.prototype.initController=function(){
    this.controller.clearAllListener();
    this.addControllerBaseEvent();
};
const controllerEventList = [
    'request_create_conversation',//请求创建会话
    'query_user_info_list',//查询用户
    'request_add_friend',//请求添加好友
    'response_add_friend',//处理添加好友请求
    'search_group',//搜索群
    'join_into_group_or_not',//审核加入群
    'add_user_favorites',//添加云收藏
    'query_user_favorites',//查询云收藏
    'delete_user_favorites',//删除云收藏
    'set_user_portrait_img',//修改头像
    'ctrl_device_event',//修改设备的FTP信息
    'qr_login_auth',
    'query_device_register_state',//查询设备注册状态
    'query_device_login_info',
    'query_service_provider_list',//查询服务器提供者列表
    'set_user_other_info',//设置用户配置
    'request_generate_referral_code',//请求生成推荐码
    'query_faq',//查询FAQ
    'search_exams',//搜索检查
    'new_exam',//新建检查
    'import_exam_images',//入库检查图像
    'push_device_info',//推送设备信息
    'request_get_thumbnail',
    'query_groupset_list',//查询群落列表
    'add_groupset',
    'update_groupset',
    'set_groupset_portrait',
    'delete_groupset_list',
    'open_conversation_wall',
    'close_conversation_wall',
    'get_groupset_exam_list',
    'get_groupset_exam_image_list',
    'query_iworks_protocol_basic_info_list',
    'query_iworks_protocol_detail_info',
    'get_statistics_command',
    'query_user_basic_info',
    'query_group_basic_info',
    'get_all_hospital_name',//查询医院信息
    'get_user_info',//查询个人信息
    'search',//主页搜索
    'get_qc_statistics_bi_url',//BI数据展示
    'add_case_date',//
    'update_case_date',//
    'query_case_date',//
]
CMainScreen.prototype.addControllerBaseEvent=function(){
    var that=this;
    for(let controllerEvent of controllerEventList){
        that.controller.on(controllerEvent,function(data,callback){
            // that.gateway.emit(controllerEvent,data,callback);
            if(typeof(data) == 'function'){
                that.gateway.emit(controllerEvent,data);
            }else{
                that.gateway.emit(controllerEvent,data,callback);
            }
        })
    }
    //MainScreen
    that.controller.on("get_consultation_image_list",function(data,callback){
        that.onGetConsultationImageList(data,callback);
    });
    //Conversation
    that.controller.on("request_start_conversation",function(data,start_type,callback){
        that.onRequestStartConversation(data,start_type,callback);
    });
    that.controller.on("request_start_single_chat_conversation",function(data,callback){
        that.onRequestStartSingleChatConversation(data,callback);
    });
};
CMainScreen.prototype.initLocalData= function(){
    var that=this;
    // that.addGatewayEvent();
    return Promise.all([
        that.getRecentActiveConversationsFromLocalSotrage,
        that.getFriendsFromLocalSotrage,
        that.getConversationsFromLocalSotrage
    ])
};
CMainScreen.prototype.initGateway= function(client_uuid){
    var that=this;
    that.client_uuid = client_uuid;
    that.gateway.connectSocket();
    console.warn('that.client_type',that.client_type)
    this.initController();
    this.addGatewayEvent();
    this.initMonitorWallPush()
};
CMainScreen.prototype.CloseSocket = function(){
    this.gateway.connected=false;
    this.gateway.closeSocket();
    for(var i in this.conversation_list) {
        this.conversation_list[i].gateway.closeSocket();
    }


}
CMainScreen.prototype.checkMainScreenSocket=function(){
    console.log('checkMainScreenSocket')
    return new Promise((resolve,reject)=>{
        let timeout = setTimeout(()=>{
            reject('custom ping time out')
        },15000)
        window.main_screen.custom_ping({requestTimeOut:15000},(res)=>{
            if(res.error_code === 0){
                resolve(true)
                clearTimeout(timeout)
                timeout = null
            }else{
                reject('custom ping error')
            }

        })
    })
}
CMainScreen.prototype.addGatewayEvent=function(){
    this.addGatewayBaseEvent();
};
const gatewayEventList = {
    'request_notifications':'group_applys',//设置入群申请
    'system_notify':'friend_applys',//设置好友申请
    'update_friend_state':'update_friend_info',//更新好友信息
    'notify_friend_destroy':'notify_friend_destroy',//推送好友注销消息
    'user_info':'user_info',//更新个人信息
    'update_user_info':'update_user_info',//更新用户信息
    'update_user_portrait_img':'update_user_portrait_img',//更新用户头像
    'agora_live_start':'notify_agora_live_start',//通知开启直播
    'agora_live_stop':'notify_agora_live_stop',//通知关闭直播
    'notify_update_recording':'notify_update_recording',//通知回放编辑有更新
    'query_user_info_list_result':'query_user_info_list_result',//查询用户列表结果
    'request_add_friend_ack':'request_add_friend_ack',//请求添加好友确认
    'response_add_friend_ack':'response_add_friend_ack',//响应添加好友确认消息
    'notify_delete_group':'notify_delete_group',
    'notify_device_event':'notify_device_event',
    'notify_add_groupset':'notify_add_groupset',
    'notify_update_groupset':'notify_update_groupset',
    'notify_update_groupset_portrait':'notify_update_groupset_portrait',
    'notify_delete_groupset_list':'notify_delete_groupset_list',
    'notify_exception':'notify_exception',//异常
    'receive_group_message':'receive_group_message',//会话未开启时，接受群消息
    'dopplerRemoteControlEvent':'dopplerRemoteControlEvent',
    'userResponseFriendApply':'userResponseFriendApply',
    'userAddFriend':'userAddFriend',
    'userApplyAddFriend':'userApplyAddFriend',
    'userAddLoginClient':'userAddLoginClient',//多端登录，置顶文件传输助手
    'notify_add_case_data':'notify_add_case_data',
    'notify_update_case_data':'notify_update_case_data',
    'notify_update_announcement':'notify_update_announcement',//通知全局公告有更新
    'notify.group.resource.delete.exam':'notify.group.resource.delete.exam',//通知检查删除
    "notify.group.resource.delete.resource":"notify.group.resource.delete.resource",//通知资源被删除
    "notify_refresh_manager_groupset_list":'notify_refresh_manager_groupset_list',//通知刷新群落授权管理列表
    "notify_refresh_my_groupset_list":'notify_refresh_my_groupset_list',//通知我的群落列表
    "update_ai_analyze_report":'update_ai_analyze_report',//通知AI分析结果
    "student_answer_sheet_update":'student_answer_sheet_update',  //云作业待完成列表更新通知
    "teacher_answer_sheet_update":'teacher_answer_sheet_update',  //云作业待批改列表更新通知
    'notify_msg_from_owner':'notify_msg_from_owner',//接受自己给自己发的通知
    'notify.user.device.sync.live':'notify.user.device.sync.live',//PC端发送远程开播指令给ULinker通知开播
    'pumch.qclive':'pumch.qclive',//协和发起直播
    'pumch.qclive.rejoin':'pumch.qclive.rejoin',//协和发起直播
    'pumch.qclive.ended':'pumch.qclive.ended',//协和结束直播
};
CMainScreen.prototype.addGatewayBaseEvent=function(){
    var that=this;
    //Gateway
    for(let gatewayKey in gatewayEventList){
        const controllerKey=gatewayEventList[gatewayKey]
        that.gateway.on(gatewayKey,function(is_succ,data){
            //部分接口并不返回is_succ只是返回一个data，部分接口不是返回is_succ而是返回err
            that.controller.emit(controllerKey, is_succ,data);
        });
    }
    that.gateway.on("notify_add_friend",function(data){
        //通知添加好友
        that.onNotifyAddFriend(data);
    });
    that.gateway.on("notify_login_another",function(){
        //异地登录
        that.onNotifyLoginAnother();
    });
    that.gateway.on("notify_user_destroy",function(){
        //用户注销
        that.onNotifyUserDestroy();
    });
    that.gateway.on("server_info",function(data){
        //服务器信息
        that.onServerInfo(data);
    });
    that.gateway.on("version_info",function(data){
        //服务器版本信息
        that.serverVersionInfo = data;
    });
    //Conversation
    that.gateway.on("notify_start_conversation",function(is_succ,conversation,start_type){
        //通知启动会话
        that.onNotifyStartConversation(is_succ,conversation,start_type);
    });
    that.gateway.on('connect',function(e){
        Logger.save({
            message:`mainScreen_connect`,
            eventType:'socket'
        })
        if(that.gateway.connected){
            return
        }
        var conversation_id_list = that.releaseConversationGatewayList();
        that.gateway.emit("check_main_screen", {
            user_id: that.uid,
            client_type: that.client_type,
            client_uuid: that.client_uuid
        }, async function (is_succ,data){
            console.log('[socket event] callback of CMainScreenGateway check_main_screen',Tool.triggerTime());
            console.log(is_succ,data);
            Logger.save({
                message:`mainScreen_check`,
                eventType:'socket',
                data:JSON.stringify([is_succ,data])
            })
            that.onGatewayConnect();
            that.releaseConversationGatewayList();
            if(is_succ) {
                //Init
                //获取最近聊天
                that.gateway.emit("get_recent_active_conversation_list");
                //获取好友列表
                that.gateway.emit("get_friend_info_list");
                //获取群列表
                that.gateway.emit("get_conversation_list");
                //获取离线消息
                that.gateway.emit("get_offline_chat_message");
                //获取离线系统通知
                that.gateway.emit("get_offline_system_notify");
                //获取离线入群申请
                that.gateway.emit('get_request_notifications');
                //获取服务器信息
                that.gateway.emit("get_server_info", window.clientType);
                //获取版本信息
                that.gateway.emit("get_version_info");
                //获取设备信息
                that.gateway.emit("get_device_info");
                // for (var i in conversation_id_list) {
                //     that.onRequestStartConversation(conversation_id_list[i]);
                // }
                let currentCid = window.vm.$route.params.cid
                if((currentCid!=='0') && currentCid){
                    that.onRequestStartConversation(currentCid);
                }
            } else {
                that.onGatewayReconnectFail();
            }

        });

    });
    that.gateway.on('error',function(e){
        that.onGatewayError(e)
    });
    that.gateway.on('disconnect',function(e){
        that.onGatewayDisconnect(e);
    });
    that.gateway.on('reconnecting',function(){
        that.onGatewayReconnecting();
    });
    that.gateway.on('reconnect_failed',function(e){
        that.onGatewayReconnectFail(e);
    });
    that.gateway.on('reconnect',function(){
        that.onGatewayReconnect();
    });
    //MainScreen
    that.gateway.on("recent_active_conversation_list",function(is_succ,data){
        //设置最近会话列表
        that.onRecentActiveConversationList(is_succ,data);
    });
    that.gateway.on("recent_active_conversation_list_last_message",function(is_succ,data){
        //设置最近会话列表最后消息
        that.onRecentActiveConversationListLastMessage(is_succ,data);
    });
    that.gateway.on("friend_info_list",function(is_succ,data){
        //设置好友列表
        that.onFriendList(is_succ,data);
    });
    that.gateway.on("conversation_list",function(is_succ,data){
        //设置会话列表
        that.onConversationList(is_succ,data);
    });
};
CMainScreen.prototype.onGatewayConnect=function(){
    //Gateway连接
    var that=this;
    that.gateway.connected=true;
    that.gateway.check = true
    that.controller.emit("gateway_connect");
    window.vm.$root.eventBus.$emit(`main_screen_gateway_connect`)
};
CMainScreen.prototype.onGatewayError=function(e){
    //Gateway错误
    var that=this;
    that.resetApp();
    that.CloseSocket();
    that.controller.closeResources();
    that.controller.emit("gateway_error",e);
    window.vm.$root.eventBus.$emit(`main_screen_gateway_error`)
};
CMainScreen.prototype.onGatewayDisconnect=function(e){
    //Gateway断开
    var that=this;
    that.controller.emit("gateway_disconnect",e);
    window.vm.$root.eventBus.$emit(`main_screen_gateway_disconnect`)
    that.gateway.connected=false;
    that.gateway.check = false
};
CMainScreen.prototype.onGatewayReconnecting=function(){
    //Gateway重连中
    var that=this;
    that.controller.emit("gateway_reconnecting");
};
CMainScreen.prototype.onGatewayReconnectFail=function(e){
    var that=this;
    that.resetApp();
    that.CloseSocket();
    that.controller.closeResources();
    that.controller.emit("gateway_reconnect_fail",e);
    window.vm.$root.eventBus.$emit(`main_screen_reconnect_fail`)
};
/*
 * @description 断开后，重置App实时等相关信息
 */
CMainScreen.prototype.resetApp = function () {
    var that=this;
};
CMainScreen.prototype.onGatewayReconnect=function(){
    var that=this;
    that.controller.emit("gateway_reconnect");
    window.vm.$root.eventBus.$emit(`main_screen_reconnect`)

};

CMainScreen.prototype.onRecentActiveConversationList=function(is_succ,data){
    //设置最近会话列表
    var that=this;
    that.controller.emit("recent_active_conversation_list",is_succ,data);
    if(is_succ) {
        this.gatewayChatList=data;
        this.isGetGatewayChatList=true;
    }
};
CMainScreen.prototype.onRecentActiveConversationListLastMessage=function(is_succ,data){
    //设置最近会话列表
    var that=this;
    that.controller.emit("recent_active_conversation_list_last_message",is_succ,data);
    if(is_succ) {
        this.gatewayLastMessages=data;
        this.isGetGatewayLastMessage=true;
    }
};
CMainScreen.prototype.onFriendList=function(is_succ,data){
    //设置好友列表
    var that=this;
    that.controller.emit("friend_list",is_succ,data);
    if(is_succ) {
        that.gatewayFriendList=data;
        that.isGetGatewayFriend=true;
    }
};
CMainScreen.prototype.onConversationList=function(is_succ,data){
    //设置会话列表
    var that=this;
    that.controller.emit("conversation_list",is_succ,data);
    if(is_succ) {
        that.gatewayGroupList=data;
        that.isGetGatewayGroup=true;
    }
};
//Other
CMainScreen.prototype.onNotifyAddFriend=function(data){
    //通知添加好友
    var that=this;
    that.controller.emit("notify_add_friend",data);
    var addFriends = [];
    addFriends.push(data);
};
CMainScreen.prototype.onNotifyLoginAnother=function(){
    //异地登录
    var that=this;
    that.CloseSocket();

    // 清理权限系统（保留区域权限）
    try {
        if (permissionManager.logoutCleanup === 'function') {
            permissionManager.logoutCleanup();
            console.log('移动端CMainScreen异地登录权限系统清理完成');
        }
    } catch (error) {
        console.error('移动端CMainScreen异地登录权限系统清理失败:', error);
    }

    that.controller.emit("notify_login_another");
    that.controller.closeResources();
};
CMainScreen.prototype.onNotifyUserDestroy=function(){
    //注销
    var that=this;
    that.CloseSocket();

    // 清理权限系统（保留区域权限）
    try {
        if (permissionManager.logoutCleanup === 'function') {
            permissionManager.logoutCleanup();
            console.log('移动端CMainScreen用户注销权限系统清理完成');
        }
    } catch (error) {
        console.error('移动端CMainScreen用户注销权限系统清理失败:', error);
    }

    that.controller.emit("notify_user_destroy");
    that.controller.closeResources();
};
CMainScreen.prototype.onServerInfo=function(data){
    //服务器信息
    var that=this;
    that.serverInfo=data;
    that.controller.emit("server_info",data);
    //修改 rtc.SERVER 的值
    //无论aliyun还是webrtc都运行下面代码，防止aliyun降级到webrtc
    var serverInfo = that.serverInfo;
    var find_ice_server = false;
    var new_turn_server = "turn:" + serverInfo.webrtc_coturn_addr + ":" + serverInfo.webrtc_coturn_port;
    for(var k in window.rtc.SERVER.iceServers){
        if(window.rtc.SERVER.iceServers[k].url == new_turn_server){
            find_ice_server = true;
            break;
        }
    }
    if(!find_ice_server){ //不存在则添加
        window.rtc.SERVER.iceServers.splice(0, 0, {
            'url': "turn:" + serverInfo.webrtc_coturn_addr + ":" + serverInfo.webrtc_coturn_port,
            'credential': serverInfo.webrtc_coturn_pwd,
            'username': serverInfo.webrtc_coturn_user,
        });
    }
    //修改rtc.audioParam.maxplaybackrate
    if(serverInfo.webrtc_maxplaybackrate){
        window.rtc.audioParam.maxplaybackrate = serverInfo.webrtc_maxplaybackrate;
    }
};
//Conversation
CMainScreen.prototype.onNotifyStartConversation=function(is_succ,conversation,start_type){
    //通知启动会话
    var that=this;
    console.log('onNotifyStartConversation',is_succ,conversation,start_type)
    if (is_succ&&conversation) {
        if (!that.conversation_list[conversation.id]) {
            var option = {
                uid:that.uid,
                client_type:that.client_type,
                client_uuid:that.client_uuid,
                url:that.url,
                conversation:conversation,
                start_type:start_type
            };
            var new_conversation = that.newConversation(option);
            that.conversation_list[conversation.id] = new_conversation;
            var new_conversation_list = [];
            new_conversation_list.push(conversation);
            conversation.controller = that.conversation_list[conversation.id].controller;
            conversation.cLiveConference = that.conversation_list[conversation.id].cLiveConference;

            // 初始化设置当前用户的会话角色
            that.setUserConversationRole(conversation);

            that.controller.emit("notify_start_conversation",is_succ,conversation,start_type);
        }
    } else {
        that.controller.emit("notify_start_conversation",is_succ,conversation,start_type);
    }
};
CMainScreen.prototype.newConversation=function(option){
    var conversation = null;
    if (ServiceConfig.type.AiAnalyze == option.conversation.service_type||ServiceConfig.type.DrAiAnalyze == option.conversation.service_type) {
        conversation = new CAiAnalyze(option);
    } else if (ServiceConfig.type.FileTransferAssistant == option.conversation.service_type) {
        conversation = new CFileTransferAssistant(option);
    } else if (ServiceConfig.type.CentralStation == option.conversation.service_type) {
        conversation = new CCentralStation(option);
    } else if (ServiceConfig.type.FeedbackQuestionAssistant == option.conversation.service_type) {
        conversation = new CFeedbackQuestionAssistant(option);
    } else {
        conversation = new CConversation(option);
    }
    return conversation;
};

CMainScreen.prototype.releaseConversationGatewayList=function(){
    var conversation_id_list = [];
    for(var i in this.conversation_list) {
        this.conversation_list[i].release();
        delete this.conversation_list[i];
        conversation_id_list.push(i);
    }
    this.conversation_list = {};
    return conversation_id_list;
};
/////////////////////////////////////////////////////////////////////////
////////////////////////////////本地存储//////////////////////////////////
/////////////////////////////////////////////////////////////////////////
CMainScreen.prototype.getFriendsFromLocalSotrage= async function(){
    const list = await window.mainDB.friendList.toCollection().toArray()
    this.localFriendList=list;
    this.isGetLocalFriend=true;
    this.controller.emit("friend_list",true,list);
};
CMainScreen.prototype.getRecentActiveConversationsFromLocalSotrage= async function(){
    const data = await window.mainDB.chatList.orderBy('last_message_ts').reverse().limit(20).toArray()
    this.controller.emit("recent_active_conversation_list",true,data,true);
    this.getRecentActiveConversationMsgFromLocalSotrage(data);
};
CMainScreen.prototype.getRecentActiveConversationMsgFromLocalSotrage= async function(list){
    const data= await window.mainDB.lastMessageList.toCollection().toArray()
    if(!data || Object.keys(data).length==0){
        return
    }
    for(var i in list){
        for(var j in data){
            if(list[i].cid == data[j].group_id){
                list[i].message = {...data[j]};
                break;
            }
        }
    }
    this.localLastMessages=data
    this.isGetLocalLastMessage=true;
    this.controller.emit("recent_active_conversation_list_last_message",true,list);
};
CMainScreen.prototype.getConversationsFromLocalSotrage = async function(){
    const list = await window.mainDB.groupList.toCollection().toArray()
    this.localGroupList=list;
    this.isGetLocalGroup=true;
    this.controller.emit("conversation_list",true,list);
};
CMainScreen.prototype.userEventV2 = function ({bizContent,method,showErrorToast=true,requestTimeOut = null},callback) {
    let defaultTimeout = requestTimeOut || 300000
    if((!window.main_screen.gateway.check)&&requestTimeOut===null){
        callback&&callback({
            error_code:-1,
            data:{},
            error_message: i18n.t('gateway_uncheck_error', { method })
        })
        console.error(`${method} gateway uncheck`)
        return
    }
    let timer = null
    timer = setTimeout(()=>{
        callback&&callback({
            error_code:-1,
            data:{},
            error_message: i18n.t('request_timeout_error', { method })
        })
        console.error(`${method} time out`)
        if(showErrorToast){
            Toast(i18n.t('request_timeout_error', { method }));
        }
    },defaultTimeout)
    this.gateway.emit("userEventV2",{bizContent,method}, (data)=>{
        if(timer){
            clearTimeout(timer)
            timer = null
        }
        if (!isNaN(data.error_code)&&data.error_code!=0) {
            let tip = '';
            if (data.key) {
                // 尝试翻译错误键
                tip = i18n.t(data.key) || i18n.t(`error.${data.key}`);
                // 如果翻译键不存在，返回原键值，则使用默认错误信息
                if (tip === data.key || tip === `error.${data.key}`) {
                    tip = i18n.t('unknown_error') + method;
                }
            } else {
                tip = i18n.t('unknown_error') + method;
            }
            if(showErrorToast){
                Toast(tip);
            }
        }
        callback&&callback(data)
    });
};
const v2InterfaceList = {
    'updateLiveBroadcast':'live.broadcast.update',//更新直播预约
    'createLiveBroadcast':'live.broadcast.create',//创建直播预约
    'getLiveCreatorList':'live.broadcast.creator.list',//获取自己创建的直播列表
    'getLiveAttendeeList':'live.broadcast.attendee.list',//获取可参与的直播列表
    'getLiveHistoryList':'live.broadcast.history.list',//获取直播回看列表
    'inviteOthersLive':'live.broadcast.invite.others',//转发邀请参加直播
    'checkJoinLiveStatus':'live.broadcast.check.status',//检测是否允许加入直播间
    'cancelLiveBroadcast':'live.broadcast.cancel',//取消直播
    'getBroadcastStatusCount':'live.broadcast.notify.count',//获取当前直播状态的数量
    'getLiveInfoById':'live.broadcast.get.info',//通过直播ID获取直播信息
    'setFriendRemark':'user.set.friend.alias',
    'sendAnalyzeClick':'ai.analyze.click.result.row',
    'remoteControlEvent':'remote.control.initiate',//发送远程控制请求
    'createCategoryByGroupResource':'group.resource.category.create',//新建标签
    'updateCategoryByGroupResource':'group.resource.category.rename',//修改标签
    'deleteCategoryByGroupResource':'group.resource.category.delete',//删除标签
    'getCategoryListByGroupResource':'group.resource.category.list',//获取标签列表
    'getResourceListByCategory':'group.resource.category.content',//获取标签内的资源列表
    'insertResourceItemToCategory':'group.resource.category.insert.item',//标签内插入资源
    'getGroupsetList':'groupset.list',//获取群落列表
    'getManagerGroupsetList':'groupset.list.by.manager',//获取授权管理的群落列表
    'getGroupsetManagerList':'groupset.manager.list',//获取群落中被授权管理员的列表
    'addGroupsetManager':'groupset.add.manager',//添加群落管理员
    'deleteGroupsetManager':'groupset.delete.manager',//删除群落管理员
    'createGroupset':'groupset.create',//创建群落
    'updateGroupset':'groupset.update',
    'getGroupsetDetail':'groupset.detail',
    'addGroupsetMember':'groupset.add.member',
    'removeGroupsetMember':'groupset.remove.member',
    'deleteGroupset':'groupset.delete',
    'removeResourceItemFromCategory':'group.resource.category.remove.item',//标签内删除资源
    'checkJoinGroupStatus':'group.check.join.status',//检测群的加入状态
    'getChannelIdByGroupId':'conference.get.channelId',//通过群id获取声网channelId
    'notifyRemoteMonitorPushDopplerResult':'monitorWall.workstation.ready',//通知电视墙进入会话方 开启推流结果
    'deviceBinding':'device.binding.group',//设备入群
    'deviceUnBinding':'device.unbinding.group',//设备解绑
    'getDeviceList':'query.group.joined.device.list',//查询设备列表
    'getTvWallToken':'tvwall.get.token',//推流器端获取加入房间所需信息
    'getOSSToken':'oss.get.temporary.token',//获取OSS TOKEN
    'deviceRename':'device.binding.rename',//设备重命名
    'reportDeviceInfo':'device.report.data',//上报设备信息
    'getDeviceNameById':'device.binding.get.name',//获取设备名
    'getShareLink':'group.resource.get.share.key',
    'shareLink':'group.resource.share',
    'createHospitalByName':'hospital.create.by.name',//添加自定义医院
    'getConferenceRefreshToken':'conference.refresh.token',//获取新的直播token
    'applyAddFriend':'user.add.friend',//请求添加好友
    'processAddFriend':'user.process.add.friend.apply',
    'editReviewRecording':'conference.update.recording',//编辑回放视频
    'getReviewDetail':'conference.get.live.statistic',//获取回放详情数据
    'insertResourceToCategoryByName':'group.resource.category.insert.item.by.categoryname',//将资源直接通过名字塞入群收藏中
    'reportReviewEvent':'conference.report.event',
    'bindMobile':'user.bind.mobile',
    'getInviteInfo':'group.get.invite.info',//解析群二维码参数
    'applyJoinGroup':'group.apply.join',//申请入群
    'searchGroupList':'group.list',//搜索群列表
    'setUserIntroduction':'user.set.introduction',//设置个人简介,
    'custom_ping':'custom_ping',
    'getOSSAk':'oss.get.ak',//内网环境下， 获取oss的ak
    'sendMsgOwner':'user.send.msg.owner',//发消息给自己
};
for(let key in v2InterfaceList){
    CMainScreen.prototype[key] = function (data,callback) {
        let bizContent = cloneDeep(data)
        let requestTimeOut = undefined
        let showErrorToast = undefined
        if(bizContent&&bizContent.hasOwnProperty('requestTimeOut')){
            requestTimeOut = bizContent.requestTimeOut
            delete bizContent.requestTimeOut
        }
        if(bizContent&&bizContent.hasOwnProperty('showErrorToast')){
            showErrorToast = bizContent.showErrorToast
            delete bizContent.showErrorToast
        }

        this.userEventV2({
            method:v2InterfaceList[key],
            bizContent,
            requestTimeOut,
            showErrorToast
        }, callback);
    }
}
CMainScreen.prototype.onGetConsultationImageList=function(oData,callback){
    //获取图像列表
    var that=this;
    that.gateway.emit("get_consultation_image_list",oData,function(is_succ,data){
        callback && callback(is_succ,data);
    })
};
CMainScreen.prototype.onRequestStartConversation= async function(data,start_type,callback){
    //启动会话
    var that=this;
    if (that.gateway.check) {
        that.gateway.emit('request_start_conversation',data,start_type,callback);
    }else{
        //在断网的情况下，打开会话
        // const res = await window.mainDB.conversationList.where({'group_id':data}).toArray()
        // console.error(res,111111)
        // that.onNotifyStartConversation(false,null,{type:0});
    };
};
CMainScreen.prototype.onRequestStartSingleChatConversation=function(data,callback){
    //请求启动会话
    // var that=this;
    this.gateway.emit("request_start_single_chat_conversation","single chat",data.list.join(),data.mode,data.type,data.start_type,callback);
};
CMainScreen.prototype.commitPasswordModify = function(data, callback){
    //修改密码
    var that=this;
    that.gateway.emit("commit_password_modify", data, callback);
};
CMainScreen.prototype.commitUserBasicInfoModify = function(data, callback){
    //修改密码
    var that=this;
    that.gateway.emit("set_user_info", data, callback);
};

CMainScreen.prototype.setUserConversationRole=function(conversation){
    // 设置当前用户在会话中的角色
    var that = this;
    try {
        // 确保权限管理器已初始化
        if (that.permissionManager && that.permissionManager.isInitialized()) {
            var conversationId = conversation.id;
            var currentUserId = that.uid;
            var role = GROUP_ROLE.NORMAL; // 默认为普通成员

            // 检查是否为群主
            if (conversation.creator_id === currentUserId && conversation.is_single_chat === 0) {
                role = GROUP_ROLE.CREATOR;
            } else if (conversation.attendeeList) {
                // 从参与者列表中查找当前用户的角色
                for (var attendeeKey in conversation.attendeeList) {
                    var attendee = conversation.attendeeList[attendeeKey];
                    if (attendee && (attendee.uid === currentUserId || attendee.userid === currentUserId)) {
                        // 根据 groupRole 确定角色
                        if (attendee.role === GROUP_ROLE.CREATOR) { // groupRole.creator
                            role = GROUP_ROLE.CREATOR;
                        } else if (attendee.role === GROUP_ROLE.MANAGER) { // groupRole.manager
                            role = GROUP_ROLE.MANAGER;
                        } else if (attendee.role === GROUP_ROLE.NORMAL) { // groupRole.normal
                            role = GROUP_ROLE.NORMAL;
                        }
                        break;
                    }
                }
            }

            // 设置用户会话角色
            that.permissionManager.setUserConversationRole(conversationId, currentUserId, role);
            console.log('移动端设置用户会话角色:', conversationId, currentUserId, role);
        } else {
            console.warn('移动端权限管理器未初始化，无法设置用户会话角色');
        }
    } catch (error) {
        console.error('移动端设置用户会话角色失败:', error);
    }
};

export default CMainScreen
