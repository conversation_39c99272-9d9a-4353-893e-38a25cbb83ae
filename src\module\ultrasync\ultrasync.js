import '../../common/console'
import Vue from 'vue'
import App from './App'
import store from './store'
import router from './router'
import i18n from '@/common/i18n'
import VueTouch from 'vue-touch'
import Loading from './directive/loading'
import mrAvatar from './MRComponents/mrAvatar'
import commonImage from './components/commonImage'
import mrHeader from './MRComponents/mrHeader'
import './lib/routeInterception'

import logReporter from './lib/logReporter';
import './directive'
// import { pwa } from '../../../config/index'
import '../../common/rem'
import VueVirtualScroller from 'vue-virtual-scroller'
import '../../../node_modules/vue-virtual-scroller/dist/vue-virtual-scroller.css'
import './lib/checkSameWindow'
import '@vant/touch-emulator';
import { Lazyload } from 'vant';
// import {initWebTracingConfig} from '@/common/web_tracing.js'
import 'plyr/dist/plyr.css';
import { pwa } from '../../../config/index'
import '@/icons/index'

// 安装权限管理器插件
import { install as PermissionPlugin } from '@/common/permission';
import permissionManager from '@/common/permission';
Vue.use(PermissionPlugin);
if (pwa) {
    require('./registerServiceWorker')
}
// initWebTracingConfig()
window.logReporter = logReporter;
let url=location.href
let search=url.split('?')[1]
search = search?search.split('#')[0]:''
window.urlHash={}
if (search) {

    let arr=search.split('&');
    for(let item of arr){
        let key=item.split('=')[0]
        let value=item.split('=')[1]
        window.urlHash[key]=value;
    }
}

Vue.use(VueTouch,{name:'v-touch'})
Vue.use(Loading)
Vue.component(commonImage.name,commonImage)
Vue.component(mrAvatar.name,mrAvatar)
Vue.component(mrHeader.name,mrHeader)
Vue.use(VueVirtualScroller)
Vue.use(Lazyload,{
    lazyComponent: true,
});

// 初始化区域权限管理器（应用启动时）
permissionManager.initializeRegionPermissions().then(() => {
    console.log('移动端区域权限管理器初始化完成');
}).catch(error => {
    console.error('移动端区域权限管理器初始化失败:', error);
});

/* eslint-disabled no-undef */
import Popover from './MRComponents/tooltips'
const customRootObject ={ //退出登录后不会被重置
    isInitVConsole:false,
    eventBus:new Vue(),
    platformToast:null, //挂载框架的Toast方法给公共文件使用，让公共文件使用Toast与框架无关
    functionalDialog:null,
    validateCaptcha:null,//阿里云校验实例
}
const rootObject ={  //退出登录后被重置
    currentActiveTab:'chat',
    socket:null,
    uploadList:[],//file文件上传队列
    transmitQueue:{},//云收藏，聊天界面转发图片消息队列
    transferExamQueue:{},//一键转发暂存队列,启动会话后从这个队列取转发文件
    transferExamCidObj:{},//一键转发文件id对应cid映射，更新文件进度从这里获取映射
    transferLocalQueue:{},//检查浏览转发暂存队列
    transmitTempList:[],//临时保存的转发对象
    transmitLiveTemp:{},//临时保存的直播预告转发对象
    deleteQueue:{},//删除列表
    importExamImageTempQueue:[],//检查图像入库临时列表
    updateGroupAvatarUserList:{}, //需要更新群头像的列表，启动会话后如果有值则对群头像进行更新
    withDrawList:{},//撤回列表
    withDrawTimeList:{},
    currentLiveCid:0,//当前正在接受或者发起直播的群id
}
Vue.use(Popover,{ tooltip: true })
window.vm=new Vue({
    el: '#app',
    store,
    router,
    i18n,
    template: '<App/>',
    components: { App },
    data(){
        return {
            ...customRootObject,
            ...rootObject
        }
    }
})
window.vm.$root.resetCustomRootObject=function(){ //重置rootObject
    Object.keys(rootObject).forEach(key=>{
        window.vm.$root[key] = rootObject[key]
    })

}
