import Vue from 'vue'
import { Toast } from 'vant';
import { cloneDeep } from 'lodash'
import service from '../service/service'
import store from '../store'
import CryptoJS from 'crypto-js';
import {newMainDB} from '@/common/indexdb/index.js'
import moment  from 'moment'
import Tool from '@/common/tool.js';
import iworksInternational from '@/common/iworksInternational.js'
import permissionManager from '@/common/permission/index.js'
import { getLanguage } from '@/common/i18n';
// import {updateWebTracingConfig} from '@/common/web_tracing.js'
import i18n from '@/common/i18n';
let root = null
setTimeout(()=>{
    root = window.vm.$root
},0)
export function back(length){
    if(window.vm.$route.name === 'index'){
        return window.vm
    }
    if(typeof length === 'number'){
        window.vm.$router.go(length*-1)
    }else{
        window.vm.$router.go(-1)
    }
    console.log('emit back')
    return window.vm;
}
export function backToIndex(){
    let pageDeep=window.vm.$route.matched.slice(1);
    if (pageDeep.length>0) {
        back(pageDeep.length)
    }
}
export function closeChatWindowIfNeed(){
    if (/chat_window/.test(window.vm.$route.fullPath)) {
        let pageDeep=window.vm.$route.matched.slice(1);
        back(pageDeep.length)
    }
}
export function closeChatWindowForce(){
    window.history.replaceState(null, null, window.location.href.replace(/(\?|\&)([^=]+)\=([^&]+)/g, '') + '?force=1');
    if (/chat_window/.test(window.vm.$route.fullPath)) {
        let pageDeep=window.vm.$route.matched.slice(1);
        console.log(pageDeep,'pageDeep')
        back(pageDeep.length)
    }

    setTimeout(()=>{
        window.history.replaceState(null, null, window.location.href.replace(/(\?|\&)(force\=1)/g, ''));
        // setTimeout(()=>{
        //
        // },300)
    },200)

}
export function closeDeviceWindowIfNeed(){
    if (/ultrasound_machine/.test(window.vm.$route.fullPath)) {
        let pageDeep=window.vm.$route.matched.slice(1);
        for(let i=0;i<pageDeep.length;i++){
            ((level)=>{
                setTimeout(()=>{
                    back()
                },200*level)
            })(i)
        }
    }
}
export function formatString(text, param){
    return text.replace(/\{(\d+)\}/gm, function(ms, p1){
        return typeof(param[p1]) == 'undefined' ? ms : param[p1]
    });
}
export function getThumb(msg_type){
    let width=window.screen.width;
    const systemConfig = store.state.systemConfig
    let thumb=''
    if (width>1200) {
        thumb='_large'
    }else if (width>=768) {
        thumb='_mid'
    }else{
        return ''
    }
    switch(msg_type){
    case systemConfig.msg_type.Image:
    case systemConfig.msg_type.Video:
        thumb="_thumb"+thumb+".jpg"
        break;
    case systemConfig.msg_type.OBAI:
    case systemConfig.msg_type.Frame:
    case systemConfig.msg_type.Cine:
        thumb="thumbnail"+thumb+".jpg"
        break;
    default:
        break;
    }
    return thumb
}
export function getImgObjUrl(imgObj, type) {
    var img_src=''
    type = parseInt(type);
    switch(type){
    case 1:
    case 4:
    case 8:
    case 12:
    case 13:
    case 14:
    case 15:
    case 16:
    case 17:
    case 19:
    case 30:
        img_src=imgObj.url;
        let target=getThumb(imgObj.msg_type);
        if (target&&imgObj.url_local&&imgObj.url_local.indexOf(target)>-1) {
            img_src=img_src.replace("_thumb.jpg",target)
            img_src=img_src.replace("thumbnail.jpg",target)
        }
        break;
    case 0:
    case 2:
        img_src=imgObj.tempRealUrl
        break;
    case 3:
    case 5:
    case 6:
    case 9:
    case 10:
    case 11:
    case 18:
    case 21:
    case 22:
    case 23:
    case 24:
    case 25:
    case 26:
    case 27:
    case 28:
        img_src=imgObj.avatar
        break;
    case 7:
        img_src=imgObj.param.avatar;
        break;
    case 20:
        img_src=imgObj.sender_nickname[0].avatar;
        break;
    default:
        console.log("imgObj type Error ############################")
        console.log(type)
    }
    return img_src;
}
export function changeDowLoadImgPriority(arr){
    const systemConfig = store.state.systemConfig
    let queue=systemConfig.downLoad.Queue
    for(let img_url of arr){
        for(let index=0;index<queue.length;index++){
            if (queue[index]&&queue[index].imgObj.url==img_url) {
                let item=queue.splice(index,1);
                queue.push(item[0]);
                var img_src= getImgObjUrl(item[0].imgObj, item[0].type);
                var local_path = img_src;
                if (img_src.match(/^http:\/\//i) || img_src.match(/^https:\/\//i)){//单帧会诊文件
                    var img_srcArr = img_src.split("mindray.com:443/");
                    if(img_srcArr.length < 2){
                        img_srcArr = img_src.split("aliyuncs.com/");
                    }
                    if(img_srcArr.length < 2){
                        img_srcArr = img_src.split("mindray.com:8081/");
                    }
                    if(img_srcArr.length < 2){
                        img_srcArr = img_src.split("mindray.com:80/");
                    }
                    if(img_srcArr.length < 2){
                        img_srcArr = img_src.split("mindray.com/");
                    }
                    local_path = img_srcArr.pop();
                }
                console.info('优先下载：',local_path)
                break
            }
        }
    }
}
export function computedImageInView(debounceType){
    //判断图像是否在视口内
    const cid = window.vm.$route.params.cid
    const conversation = cid?store.state.conversationList[cid]:{}
    const galleryList = cid?conversation.galleryObj.gallery_list:[]
    const consultationImageList = store.state.consultationImageList.list
    const systemConfig = store.state.systemConfig
    if (systemConfig.downLoad.Queue.length==0) {
        //没有下载任务无需判断
        return ;
    }
    let container=''
    let list=[];
    let itemClass='.file_item'
    if (debounceType==1) {
        //图像列表滚动触发
        container=document.querySelector('.file_list_page')
        list=consultationImageList||[];

    }else if(debounceType==2){
        //所有群文件滚动触发
        container=document.querySelector('.group_all_files')
        list=galleryList||[];
    }else if (debounceType==3) {
        container=document.querySelector('.message_list_container')
        list=conversation.chatMessageList||[]
        itemClass='.message_item'
    }else if (debounceType==4) {
        container=document.querySelector('.exam_list')
        let examObj=store.state.examList[cid]
        for(let item of examObj.list){
            list=list.concat(item.iworksImages)||[]
            list=list.concat(item.noneIworksImages)||[]
        }
    }else{
        //默认为图像列表滚动触发
        container=document.querySelector('.file_list_page')
        list=consultationImageList||[];
    }
    let arr=[];
    let viewTop=container.scrollTop
    let viewBottom=container.scrollTop+container.clientHeight;
    let fileList=container.querySelectorAll(itemClass)
    let startInViewIndex=0;//开始进入视口的图片下标
    let endInViewIndex=0;//开始离开视口的图片下标
    let preArr=[];//视口前后各12张图片
    for(let index=0;index<fileList.length;index++){
        let file=fileList[index];
        let itemTop=file.offsetTop
        let itemBottom=file.offsetTop+file.clientHeight
        if (itemBottom>=viewTop&&itemTop<viewBottom) {
            startInViewIndex=index;
            if (list[index].url_local=='static/resource/images/placeholder.png') {
                arr.push(list[index].url);
            }
        }else{
            if (startInViewIndex) {
                endInViewIndex=index;
                //离开视口不再进行判断
                break;
            }
        }
    }
    if (debounceType==1||debounceType==2) {
        if (startInViewIndex) {
            for(let index=12;index>0;index--){
                if (startInViewIndex-index>=0&&list[startInViewIndex-index].url_local=='static/resource/images/placeholder.png') {
                    preArr.push(list[startInViewIndex-index].url)
                }
            }
        }
        if (endInViewIndex) {
            for(let index=1;index<=12;index++){
                if (list[startInViewIndex+index]&&list[startInViewIndex+index].url_local=='static/resource/images/placeholder.png') {
                    preArr.push(list[startInViewIndex+index].url)
                }
            }
        }
    }
    changeDowLoadImgPriority(preArr.concat(arr));
    console.log('computedImageInView',arr);
}
export function findServiceId(service_type){
    //找到迈瑞AI的cid
    return new Promise((resolve,reject)=>{
        let chatList=store.state.chatList.list;
        let cid,id;
        for(let item of chatList){
            if (item.service_type==service_type) {
                cid=item.cid;
                break;
            }
        }
        if (!cid) {
            let friendList=store.state.friendList.list;
            for(let item of friendList){
                if (item.service_type==service_type) {
                    id=item.id;
                    break;
                }
            }
        }
        if (cid||id) {
            resolve({
                cid:cid,
                id:id
            })
        }else{
            window.main_screen.controller.emit('query_service_provider_list',(is_succ,data)=>{
                if (is_succ) {
                    if (data.length==0) {
                        Toast(i18n.t('banned_this_moment'));
                    }
                    for(let item of data){
                        if (item.service_type==service_type) {
                            id=item.id;
                            break;
                        }
                    }
                    resolve({
                        cid:cid,
                        id:id
                    })
                }
            })
        }
    })
}
export async function sendToAnalyze(){
    const systemConfig = store.state.systemConfig
    let analyze=await findServiceId(systemConfig.ServiceConfig.type.AiAnalyze)
    analyze.ai_share_to_group=true;
    root.eventBus.$emit('generalTransmitSubmit',analyze)
}
export function doSaveDownload(imgObj,img_src_http,type){

}
export function htmlEscape(text = '') {
    var map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };

    // 这次将所有的 & 和 < 都转义，而不排除任何标签
    return text.replace(/[&<>"]/g, function (m) {
        return map[m] || m;
    });
}
export function htmlUnescape(str=''){
    str=str.replace(/&amp;/g,'&')
    str=str.replace(/&lt;/g,'<')
    str=str.replace(/&gt;/g,'>')
    str=str.replace(/&quot;/g,'"')
    str=str.replace(/&#x27;/g,"'")
    str=str.replace(/&#x2F;/g,'/')
    return str
}
export function getFullServerResourceUrl(img_src){ // 获取完整的服务器资源地址
    const systemConfig = store.state.systemConfig
    let socketServer=systemConfig.server_type.protocol+systemConfig.server_type.host+systemConfig.server_type.port
    var full_url = socketServer + "/" + img_src;//完整的服务器资源地址
    let abs_url=img_src//请求生成缩略图的地址
    if (img_src.match(/^http:\/\//i) || img_src.match(/^https:\/\//i)){//单帧会诊文件
        full_url = img_src;
    }else{
        abs_url="/"+img_src
    }
    return {
        full_url,
        abs_url
    }
}
export  function isSmartIworks(exam) { //画廊内弹提示
    if(exam && exam.exam_type){
        return exam.exam_type == 4 || exam.exam_type == 2;
    }
    return false;
}
export function getLocalImgUrl(img_src='',forceDownload) {
    const user = store.state.user
    const systemConfig = store.state.systemConfig
    var local_http = 'file://';
    var local_path = '';//相对路径
    var img_src_http = img_src;
    let isApp = systemConfig.client_type.AppMobile == window.clientType || systemConfig.client_type.UltraSoundMobile == window.clientType
    const noDownloadImage = window.vm.$store.state.globalParams.noDownloadImage
    if((isApp&&!noDownloadImage)||forceDownload) {//mobile app.
        if (img_src.match(/^file:\/\//i)) {//如果已经是本地地址
            img_src_http = img_src;
        }else{
            if (img_src.match(/^http:\/\//i) || img_src.match(/^https:\/\//i)){//单帧会诊文件
                var img_srcArr = img_src.split("mindray.com:443/");
                if(img_srcArr.length < 2){
                    img_srcArr = img_src.split("aliyuncs.com/");
                }
                if(img_srcArr.length < 2){
                    img_srcArr = img_src.split("mindray.com:8081/");
                }
                if(img_srcArr.length < 2){
                    img_srcArr = img_src.split("mindray.com:80/");
                }
                if(img_srcArr.length < 2){
                    img_srcArr = img_src.split("mindray.com/");
                }
                local_path = img_srcArr.pop();
            } else {
                local_path = img_src;
            }

            var sd_path_cell1 = '_documents/Ruitong/'+systemConfig.server_type.host+'/'+user.uid+'/images/'+local_path;
            var sd_path_cell2 = ''
            if(true){
                try {
                    Tool.createCWorkstationCommunicationMng({
                        name: "convertLocalFileSystemURL",
                        emitName: 'NotifyConvertedLocalFileSystemURL',
                        params:{url: sd_path_cell1},
                        timeout:1500
                    }).then((res)=>{
                        sd_path_cell2 = res.url
                        img_src_http = local_http + sd_path_cell2;
                    })
                }catch(error){
                    Toast(error);
                }
            }
            // img_src_http = local_http + sd_path_cell2;
        }
    }else{
        img_src_http = getFullServerResourceUrl(img_src).full_url
    }
    // console.log('user.uid',user.uid)
    // console.log('getLocalImgUrl',img_src_http)
    return img_src_http;
}
export function getThumbnailLocalImgUrl(img_src='',msg_type){
    let target=getThumb(msg_type)
    if (target) {
        img_src=img_src.replace("_thumb.jpg",target)
        img_src=img_src.replace("thumbnail.jpg",target)
    }
    return getLocalImgUrl(img_src);
}
export function getRealUrl(imageObj){
    let localRealUrl=''
    let serverRealUrl=''
    let url_local=getLocalImgUrl(imageObj.url)
    const systemConfig = store.state.systemConfig
    let isApp = systemConfig.client_type.AppMobile == window.clientType || systemConfig.client_type.UltraSoundMobile == window.clientType
    if (isApp){
        //生成大缩略图在内网下upload/xxx地址需加上/
        if (!/https?:\/\//.test(imageObj.url)) {
            serverRealUrl='/'
        }
    }
    let msg_type = imageObj.img_type_ex||imageObj.msg_type
    switch(msg_type){
    case systemConfig.msg_type.Image:
        localRealUrl=url_local.replace(imageObj.thumb,"");
        serverRealUrl=serverRealUrl +imageObj.url.replace(imageObj.thumb,"");
        break;
    case systemConfig.msg_type.Video:
        localRealUrl=url_local.replace(imageObj.thumb,imageObj.poster);
        serverRealUrl=serverRealUrl +imageObj.url.replace(imageObj.thumb,imageObj.poster);
        break;
    case systemConfig.msg_type.OBAI:
        localRealUrl=url_local.replace("thumbnail.jpg","ScreenShot.jpg");
        serverRealUrl=imageObj.url.replace("thumbnail.jpg","ScreenShot.jpg");
        break;
    case systemConfig.msg_type.Frame:
        localRealUrl=url_local.replace("thumbnail.jpg","SingleFrame.jpg");
        serverRealUrl=imageObj.url.replace("thumbnail.jpg","SingleFrame.jpg");
        break;
    case systemConfig.msg_type.Cine:
        localRealUrl=url_local.replace("thumbnail.jpg","DevicePoster.jpg");
        serverRealUrl=imageObj.url.replace("thumbnail.jpg","DevicePoster.jpg");
        break;
    default:
        break;
    }
    return {
        localRealUrl:localRealUrl,
        serverRealUrl:serverRealUrl
    }
}

export function setProtocolTree(protocol, countObj){
    let node={}
    if (!countObj) {
        //传递一个计数引用，给每个节点记录顺序
        countObj={
            count:0
        }
    }
    const currentLang = getLanguage() === 'CN'?'CN':'EN';
    const iworksMap = iworksInternational[currentLang];
    node.label=iworksMap[protocol.attributes.name] ||protocol.attributes.name + (protocol.attributes.Suffix||'');
    node.type=protocol.type;
    node.GUID=protocol.attributes.GUID;
    node.StructureID=protocol.attributes.StructureID;//是高帧率造影
    node.id=protocol.id;
    node.disabled=true;
    node.index=countObj.count++
    if (protocol.child_nodes) {
        node.children=[]
        for(let child of protocol.child_nodes){
            node.children.push(setProtocolTree(child,countObj));
        }
    }
    if (2 == protocol.type && protocol.attributes && protocol.attributes.child_nodes) {
        let image_mode = "";
        for(let child of protocol.attributes.child_nodes){
            if (3 == child.type && child.attributes && child.attributes.ImageMode) {
                image_mode = child.attributes.ImageMode;
                break;
            }
        }

        if (image_mode) {
            node.label = image_mode + " " + node.label;
        }
    }
    return node;
}
export function getIndexedList(list,key){
    var indexed=[{title:'A',list:[]},{title:'B',list:[]},{title:'C',list:[]},{title:'D',list:[]},{title:'E',list:[]},{title:'F',list:[]},{title:'G',list:[]},{title:'H',list:[]},{title:'I',list:[]},{title:'J',list:[]},{title:'K',list:[]},{title:'L',list:[]},{title:'M',list:[]},{title:'N',list:[]},{title:'O',list:[]},{title:'P',list:[]},{title:'Q',list:[]},{title:'R',list:[]},{title:'S',list:[]},{title:'T',list:[]},{title:'U',list:[]},{title:'V',list:[]},{title:'W',list:[]},{title:'X',list:[]},{title:'Y',list:[]},{title:'Z',list:[]},{title:'#',list:[]},
    ]
    for(var i=0;i<list.length;i++){
        //将字母，汉字首拼音分类放入Indexed数组，其他的放在#
        var sing=list[i][key].substring(0,1).toUpperCase()
        var ch = sing.charCodeAt(0);
        if(ch>=65&&ch<=90){
            indexed[ch-65].list.push(list[i])
        }else if(ch>=19968&&ch<=40869){
            var letter=window.strChineseFirstPY.charAt(ch-19968)
            var temp=letter.charCodeAt(0)
            indexed[temp-65].list.push(list[i])
        }else{
            indexed[26].list.push(list[i])
        }
    }
    return indexed
}
export function findProtocolViewNode(protocolTree,protocol_view_guid){
    if (!protocol_view_guid) {
        return null
    }
    if (protocolTree.children.length==0) {
        return null
    }
    let nextChildNodes=[];
    for(let node of protocolTree.children){
        if (node.children) {
            nextChildNodes=nextChildNodes.concat(node.children)
        }
        if (node.GUID==protocol_view_guid) {
            return node;
        }
    }
    return findProtocolViewNode({children:nextChildNodes},protocol_view_guid);
}

export function hasAiAnalyzeResult(item){
    if(item.resource_id){
        let storeItem = store.state.gallery.commentObj[item.resource_id]
        if(storeItem&&storeItem.ai_analyze_report&&storeItem.ai_analyze_report.status){
            return true
        }
    }
    if(item.ai_analyze_report && item.ai_analyze_report.status){
        return true
    }
    return false
}
export function isIworksTest(exam,protocol=''){
    let flag = false
    if(!exam.exam_type){
        return false
    }
    let views = null
    if(isSmartIworks(exam)){
        if(exam.exam_type == 4){
            views = window.vm.$store.state.aiPresetData.iworksAbdomenTest?.views||[]
        }else if(exam.exam_type == 2){
            views = window.vm.$store.state.aiPresetData.cardiacViews?.views||[]
        }else{
            return false
        }
    }
    if(!protocol){
        if(exam.iworks_protocol_execution&&exam.iworks_protocol_execution.child_nodes&&exam.iworks_protocol_execution.child_nodes.length>0){
            protocol = exam.iworks_protocol_execution
            protocol.protocolTree=[setProtocolTree(protocol)]
        }
        if(store.state&&store.state.gallery&&store.state.gallery.iworks_protocol_list){
            protocol = protocol||store.state.gallery.iworks_protocol_list[exam.protocol_execution_guid]
        }
        // if(exam.iworks_protocol&&exam.iworks_protocol.child_nodes&&exam.iworks_protocol.child_nodes.length>0){
        //     protocol = exam.iworks_protocol
        // }
    }
    if(protocol && views){
        let child_nodes = protocol.child_nodes||protocol.children

        child_nodes.map(item=>{
            if(flag){
                return true;
            }
            if(item.attributes&&item.attributes.StructureID){
                views.map(v=>{
                    if(v.key == item.attributes.StructureID){
                        flag = true
                        return true
                    }
                })
            }
        })
    }
    return flag
}
export function findProtocolViewNodeByStructId(protocolTree,structure_id){
    if (!structure_id) {
        return null
    }
    if (protocolTree.children.length==0) {
        return null
    }
    let nextChildNodes=[];
    for(let node of protocolTree.children){
        if (node.children) {
            nextChildNodes=nextChildNodes.concat(node.children)
        }
        if (node && node.StructureID && node.StructureID==structure_id) {
            return node;
        }
    }
    return findProtocolViewNode({children:nextChildNodes},structure_id);
}



export function setIworksInfoToMsg(msg,protocol_execution_guid){
    let guid=msg.protocol_execution_guid||protocol_execution_guid
    // let protocolInfo=store.state.gallery.iworks_protocol_list[guid]
    let protocolInfo=''
    if(store.state&&store.state.gallery&&store.state.gallery.iworks_protocol_list){
        protocolInfo = store.state.gallery.iworks_protocol_list[guid]
    }
    if (!protocolInfo) {
        if(store.state&&store.state.gallery&&store.state.gallery.iworks_protocol_list){
            protocolInfo = store.state.gallery.iworks_protocol_list[guid]
        }
    }
    if(!protocolInfo && msg.iworks_protocol_execution){
        protocolInfo = msg.iworks_protocol_execution
        protocolInfo.protocolTree=[setProtocolTree(protocolInfo)]
    }
    if (guid && !protocolInfo) {
        //协议信息还没推送到
        setTimeout(()=>{
            setIworksInfoToMsg(msg);
        },300)
        return ;
    }else if (protocolInfo) {
        const currentLang = getLanguage() === 'CN'?'CN':'EN';
        const iworksMap = iworksInternational[currentLang];
        let protocolTree=protocolInfo.protocolTree[0]
        msg.protocol_name=''
        msg.protocol_view_name=''
        msg.protocol_node_id=0
        if (protocolTree) {
            msg.protocol_name= iworksMap[protocolTree.label] ||protocolTree.label;
            let node=findProtocolViewNode(protocolTree,msg.protocol_view_guid)
            if (node) {
                msg.protocol_view_name=iworksMap[node.label]||node.label
                msg.protocol_node_id=node.id
            }else{
                let iworksTestFlag = isIworksTest({...msg,iworks_protocol_execution:protocolInfo},protocolInfo)
                if(iworksTestFlag&&isSmartIworks(msg) && hasAiAnalyzeResult(msg)){
                    //腹部检查-存在ai数据
                    let storeItem = store.state.gallery.commentObj[msg.resource_id]||msg
                    let ai_analyze_report = storeItem.ai_analyze_report
                    let structure_id = ''
                    if(ai_analyze_report
                        &&ai_analyze_report.clips
                        &&ai_analyze_report.clips[msg.resource_id]
                        &&ai_analyze_report.clips[msg.resource_id][0]
                        &&ai_analyze_report.clips[msg.resource_id][0].clip_id){
                        structure_id = ai_analyze_report.clips[msg.resource_id][0].clip_id
                    }
                    msg.isIworksTest = iworksTestFlag
                    if (protocolInfo&&structure_id) {
                        let protocolTree=protocolInfo.protocolTree[0]
                        let node=findProtocolViewNodeByStructId(protocolTree,structure_id)
                        if (node) {
                            msg.protocol_node_id=node.id
                            msg.LABEL_CEUS=node.LABEL_CEUS
                            msg.LABEL_CEUS_HIFR=node.LABEL_CEUS_HIFR
                            msg.protocol_view_guid=node.GUID
                            msg.protocol_execution_guid=protocolInfo.attributes.GUID
                            msg.protocol_view_name=structure_id in iworksMap? iworksMap[structure_id] : node.label
                            msg.protocol_node_id=node.id
                        }
                    }
                }
            }
        }
    }
}
export function setExpirationResource(data,cid){ // 设置失效资源类型
    const systemConfig = store.state.systemConfig
    data=data.map(item=>{
        if (item.msg_type==systemConfig.msg_type.EXAM_IMAGES) {
            return item;
        }
        if(item.resource_deleted ==true){
            item.msg_type = systemConfig.msg_type.EXPIRATION_RES // 失效资源类型
        }
        if(item.ai_analyze&&item.ai_analyze.messages){
            item.ai_analyze.messages = item.ai_analyze.messages.reduce((h,v)=>{
                if(v.resource_deleted ==true){
                    v.msg_type = systemConfig.msg_type.EXPIRATION_RES // 失效资源类型
                }
                h.push(v)
                return h
            },[])
        }
        return item
    })
    return data
}
// 检查图片完整性
export function checkImgExist(src) {
    return new Promise((resolve, reject) => {
        const img = document.createElement('img')
        img.onload = () => {
            resolve(img)
        }
        img.onerror = err => {
            reject(err)
        }
        img.src = src
    })
}
export function setWithDrawData(data){
    const systemConfig = store.state.systemConfig
    data=data.map(item=>{
        if(item.been_withdrawn ==2){
            item.msg_type = systemConfig.msg_type.WITHDRAW // 已撤回消息
        }
        return item
    })
    return data
}
export function addRootToUrl(img_src){
    const systemConfig = store.state.systemConfig
    let isApp = systemConfig.client_type.AppMobile == window.clientType || systemConfig.client_type.UltraSoundMobile == window.clientType
    if (isApp) {
        if (window.location.protocol.indexOf('file:')>=0&&!/http/.test(img_src)&&img_src[0]!='/') {
            img_src='/'+img_src
        }
    }
    return img_src;
}
export function parseImageListToLocal(list,key){
    if (list != undefined){
        for(let item of list){
            if(item[key]){
                //有local就不替换
                item[key]=addRootToUrl(item[key]);
                if (!item[key+'_local'] || item[key+'_local'] == "undefined") {
                    if (key=='url') {
                        item['url_local']=getThumbnailLocalImgUrl(item[key],item.msg_type)
                    }else{
                        item[key+'_local']=getLocalImgUrl(item[key])
                    }

                }
            }
        }
    }
    return list;
}
export function getDefaultImg(obj){
    let url=''
    if(obj.type==3){
        url='static/resource/images/groupset.png'
    }else if (obj.type==2||obj.is_single_chat===0) {
        url='static/resource/images/b1.png'
    }else{
        if (obj.sex==1) {
            url='static/resource/images/b3-1.png'
        }else{
            url='static/resource/images/b2-1.png'
        }
    }
    url=addRootToUrl(url);
    return url
}
export function updateAvatarToSearchList(imgObj, url){
    // level=1 更新第一层搜索页头像  level=2 更新第二层搜索页头像
    // 更新第一层搜索页数据
    console.log("############imgObj.level:",imgObj.level)
    if(imgObj.level === 1) {
        switch(imgObj.category) {
        // 1:更新联系人头像
        // 2:更新群聊头像
        // 3:更新最近聊天头像
        // 4:更新群落头像
        case 1:
            store.commit("relatedSearchList/updateAvatarToRelatedList", {
                avatar_local:url,
                id: imgObj.id,
                key:'relatedFriendList'
            })
            break;
        case 2:
            store.commit('relatedSearchList/updateAvatarToRelatedList', {
                avatar_local:url,
                id: imgObj.id,
                key:'relatedGroupList'
            })
            break;
        case 3:
            store.commit('relatedSearchList/updateAvatarToRelatedList', {
                avatar_local: url,
                cid: imgObj.cid,
                key:'relatedChatList'
            })
            break;
        case 4:
            store.commit('relatedSearchList/updateAvatarToRelatedList', {
                avatar_local: url,
                cid: imgObj.cid,
                key:'relatedGroupsetList'
            })
            break;
        default:
            console.log("updateAvatarToSearchList Error.Please check if the imgObj.category is right or not", imgObj)
        }
    }else if(imgObj.level === 2) { // 更新第二层搜索页数据
        switch(imgObj.category) {
        // 1:更新联系人头像
        // 2:更新群聊头像
        // 3:更新最近聊天头像
        // 4:更新群落头像
        case 1:
            store.commit("relatedSearchList/updateAvatarToRelatedList", {
                avatar_local:url,
                id: imgObj.id,
                key:'relatedMoreFriendList'
            })
            break;
        case 2:
            store.commit('relatedSearchList/updateAvatarToRelatedList', {
                avatar_local:url,
                id: imgObj.id,
                key:'relatedMoreGroupList'
            })
            break;
        case 3:
            store.commit('relatedSearchList/updateAvatarToRelatedList', {
                avatar_local: url,
                cid: imgObj.cid,
                key:'relatedMoreChatList'
            })
            break;
        case 4:
            store.commit('relatedSearchList/updateAvatarToRelatedList', {
                avatar_local: url,
                cid: imgObj.cid,
                key:'relatedMoreGroupsetList'
            })
            break;
        default:
            console.log("updateAvatarToSearchList Error.Please check if the imgObj.category is right or not", imgObj)
        }
    }
}
export function setImgObjUrl(imgObj, type, url) {
    let index=imgObj.index
    console.log('setImgObjUrl',imgObj)
    console.log(`setImgObjUrl type:${type},url:${url}`)
    type = parseInt(type);
    if (type==1 && index != -1) {
        store.commit('consultationImageList/updateImageLocalUrl',{
            imgObj:imgObj,
            url_local:url
        })
    }else if(type==2){
        if (url!='static/resource/images/placeholder.png') {
            //此类型下不替换loading
            store.commit('gallery/updateGalleryItemLocal',{
                imgObj:imgObj,
                realUrl:url
            })
        }
    }else if(type==3){
        if (url=='static/resource/images/placeholder.png') {
            url=getDefaultImg(imgObj);
        }
        store.commit('conversationList/updateMessageAvatarLocalUrl',{
            cid:imgObj.group_id,
            gmsg_id:imgObj.gmsg_id || 0,
            index:index,
            avatar_local:url
        })
    }else if(type==4){
        let cid=imgObj.group_id
        store.commit('conversationList/updateMessageImageLocalUrl',{
            index:index,
            gmsg_id:imgObj.gmsg_id || 0,
            cid:cid,
            url_local:url
        })
    }else if(type==5){
        if (url=='static/resource/images/placeholder.png') {
            url=getDefaultImg(imgObj);
        }
        store.commit('friendList/updateFriendAvatarLocalUrl',{
            id:imgObj.id,
            avatar_local:url
        })
    }else if(type==6){
        if (url=='static/resource/images/placeholder.png') {
            url=getDefaultImg(imgObj);
            // if (url=='static/resource/images/b1.png') {
            //     return ;
            // }
        }
        imgObj.avatarCallback(url);
        // store.commit('chatList/updateChatAvatarLocalUrl',{
        //     imgObj:imgObj,
        //     avatar_local:url
        // })
    }else if(type==7){
        if (url=='static/resource/images/placeholder.png') {
            url=getDefaultImg(imgObj);
        }
        store.commit('notifications/updateApplyFriendLocalUrl',{
            imgObj:imgObj,
            avatar_local:url
        })
    }else if(type==8){
        let cid=imgObj.group_id
        store.commit('conversationList/updateGroupSettingLocalUrl',{
            imgObj:imgObj,
            cid:cid,
            url_local:url
        })
    }else if(type==9){
        if (url=='static/resource/images/placeholder.png') {
            url=getDefaultImg(imgObj);
        }
        let cid=imgObj.groupid;
        let obj={
            uid:imgObj.userid,
            cid:cid,
            avatar_local:url
        }
        store.commit('conversationList/updateAttendeeLocalUrl',obj)
    }else if(type==10){
        if (url=='static/resource/images/placeholder.png') {
            url=getDefaultImg(imgObj);
        }
        store.commit('user/updateUser',{
            avatar_local:url
        })
    }else if(type==11){
        if (url=='static/resource/images/placeholder.png') {
            url=getDefaultImg(imgObj);
        }
        imgObj.avatar_local=url
    }else if(type==12){
        store.commit('userFavorites/updateFavoritesLocalUrl',{
            url_local:url,
            resource_id:imgObj.resource_id
        })
        // imgObj.url_local=url
    }else if(type==14){
        root.eventBus.$emit('updateExamPageImage',{
            url:url,
            imgObj:imgObj
        })
    }else if(type==15){
        store.commit('repository/updateRepositoryImageLocal',{
            url_local:url,
            index:index
        })
    }else if(type==16){
        store.commit('repository/updateRepositoryImageLocal',{
            url_local:url,
            index:index
        })
    }else if(type==17){
        store.commit('deviceCtrl/updateSavedImage',{
            url_local:url,
            index:index,
            device_id:imgObj.device_id
        })
    }else if(type==18){
        if (url=='static/resource/images/placeholder.png') {
            url=getDefaultImg(imgObj);
        }
        store.commit('relationship/updatePersonalObj',{
            avatar_local:url
        })
    }else if(type==19){
        let cid=imgObj.group_id
        console.log('updateAnalyzeImageLocalUrl index--',index)
        store.commit('conversationList/updateAnalyzeImageLocalUrl',{
            index:index,
            gmsg_id:imgObj.gmsg_id || 0,
            cid:cid,
            url_local:url
        })
    }else if(type==20){
        let cid=index
        store.commit('examList/updateUploaderAvatar',{
            cid:cid,
            avatar_local:url,
            exam_id:imgObj.exam_id
        })
    }else if(type==21){
        store.commit('groupList/updateAvatarToGroupList',{
            cid:imgObj.id,
            avatar_local:url,
            avatar:imgObj.avatar
        })
    }else if(type==22){
        imgObj.avatar_local=url
    }else if(type==23){
        if (url=='static/resource/images/placeholder.png') {
            url=getDefaultImg(imgObj);
        }
        store.commit('relationship/updateGroupObj',{
            avatar_local:url
        })
    }else if(type==24){
        if (url=='static/resource/images/placeholder.png') {
            url=getDefaultImg(imgObj);
        }
        store.commit('conversationList/updateConversationAvatar',{
            cid: imgObj.id,
            avatar_local:url,
            avatar: url
        })
    }else if(type==25){
        if (url=='static/resource/images/placeholder.png') {
            url=getDefaultImg(imgObj);
        }
        store.commit('user/updateUser',{
            avatar_local:url,
        })
    }else if(type==26){
        if (url=='static/resource/images/placeholder.png') {
            url=getDefaultImg(imgObj);
        }
        // 更新当前群落
        store.commit('groupset/updateCurrentGroupset',{
            avatar_local:url,
        })
    }else if(type==27){
        if (url=='static/resource/images/placeholder.png') {
            url=getDefaultImg(imgObj);
        }
        console.log("###########",imgObj)
        updateAvatarToSearchList(imgObj, url)
    }else if(type==28){
        if (url=='static/resource/images/placeholder.png') {
            url=getDefaultImg(imgObj);
        }
    }else if(type==29){
        Vue.set(imgObj,'avatar_local',url)
    }else if(type==30){
        root.eventBus.$emit('updateGroupsetExamPageImage',{
            url:url,
            imgObj:imgObj
        })
    }
}
export function enterEditMode(e){
    console.log('click editable div')
    e.currentTarget.contentEditable=true;
    e.currentTarget.focus()
}
export function changeTextTag(innerHTML){
    let emojiReg=/[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/ig;
    if (emojiReg.test(innerHTML)) {
        innerHTML=innerHTML.replace(emojiReg,'');
        Toast(i18n.t('tag_emoji_err'))
    }
    return innerHTML
    // window.vm.tagText=window.vm.$refs.tag_text.innerHTML
}
export function transferPatientInfo(param){
    if (!param.patient_id) {
        return {};
    }
    let obj={}
    obj.patient_name=param.patient_name||i18n.t('not_upload_text');
    obj.patient_name_str=param.patient_name||i18n.t('exam_patient_name')+': '+i18n.t('not_upload_text');
    switch(param.patient_sex){
    case 0:
        obj.patient_sex = i18n.t('male');
        obj.patient_sex_str = i18n.t('male');
        break;
    case 1:
        obj.patient_sex = i18n.t('female');
        obj.patient_sex_str = i18n.t('female');
        break;
    default :
        obj.patient_sex = i18n.t('not_upload_text');
        obj.patient_sex_str = i18n.t('exam_patient_sex') + ': ' +i18n.t('not_upload_text');
        break;
    }
    if (param.hasOwnProperty('patient_age')&& param.patient_age!=-1 ) {
        var  patient_age_unit=''
        switch (param.patient_age_unit) {
        case 21003:
            patient_age_unit = i18n.t('age_unit_year');
            break;
        case 21002:
            patient_age_unit = i18n.t('age_unit_month');
            break;
        case 21000:
            patient_age_unit = i18n.t('cloud_statistics_day_text');
            break;
        default :
            break;
        }
        obj.patient_age = param.patient_age + patient_age_unit;
        obj.patient_age_str=obj.patient_age = param.patient_age + patient_age_unit;
    } else {
        obj.patient_age = i18n.t('not_upload_text');
        obj.patient_age_str =i18n.t('exam_patient_age') + ': '+ i18n.t('not_upload_text');
    }
    if(param.hasOwnProperty('exam_custom_info')){
        obj.exam_custom_info = param.exam_custom_info
    }
    return obj;
}
export function cancelFavoriteCommit(list,callback){
    let arr=[];
    for(let item of list){
        arr.push(item.resource_id)
    }
    service.cancelFavorite({
        resourceIDList:arr
    }).then((res)=>{
        if (res.data.error_code==0) {
            callback&&callback();
            for(let msg of list){
                let data={
                    img:{
                        cid:msg.cid,
                        resource_id:msg.resource_id,
                    },
                    status:false,
                }
                window.vm.$store.commit('resourceTempStatus/updateResourceTempStatus',{
                    resource_id:msg.resource_id,
                    data:{
                        userFavoriteStatus:false,
                    }
                })
            }
        }
    })
}
export function getPickerDate(timestamp){
    if (!timestamp) {
        return ''
    }
    let time=new Date(timestamp);
    let year=time.getFullYear();
    let month=time.getMonth()+1;
    let day=time.getDate();
    month=month>9?month:'0'+month;
    day=day>9?day:'0'+day;
    return year+'-'+month+'-'+day;
}
export function getTimestamp(){
    var now=new Date()
    let year=now.getFullYear()
    let month=now.getMonth()+1
    let day=now.getDate();
    let hour=now.getHours();
    let minutes=now.getMinutes();
    let second=now.getSeconds();
    return {
        time:`${year}-${month>9?month:"0"+month}-${day>9?day:"0"+day} ${hour>9?hour:"0"+hour}:${minutes>9?minutes:"0"+minutes}:${second>9?second:"0"+second}`,
        timestamp:now.valueOf()
    }
}
export function sendTransferLocal(cid){
    const systemConfig = store.state.systemConfig
    const user = store.state.user
    let img=root.transferLocalQueue[cid];
    let msg={
        file_id:cid+'-'+user.uid+'-'+new Date().valueOf(),
        group_id:cid,
        msg_type:img.msg_type,
    }
    //会诊文件更改为普通文件，不带任何病人信息
    if (img.msg_type==systemConfig.msg_type.Cine) {
        msg.url=img.url2;
        msg.url_local=img.url2;
        msg.msg_type=systemConfig.msg_type.Video
    }else{
        msg.url=img.realUrl;
        msg.url_local=img.realUrl;
        msg.msg_type=systemConfig.msg_type.Image
    }
    let arr=msg.url.split('/');
    msg.file_name=arr[arr.length-1];
    msg.sender_id=user.uid //推回聊天记录时前端匹配头像和昵称用
    msg.group_id=cid;
    msg.sending=false;
    msg.sendFail=false;
    msg.uploading=true;
    msg.uploadFail=false;
    msg.percent=0;
    msg.tmp_gmsg_id=getTimestamp().timestamp
    msg.timestamp=getTimestamp().time
    msg.msg_body=JSON.stringify({
        file_id:msg.file_id,
        file_name:msg.file_name,
        original_file_name:msg.file_name,
        attachment_storage_type:systemConfig.serverInfo.attachment_storage_type,
        thumb:'_thumb.jpg'
    })
    //设置到chatList最后一条消息
    store.commit('chatList/addMessage',msg)
    let obj={
        list:[msg],
        cid:cid,
        type:'append'
    }
    store.commit('conversationList/setChatMessage',obj)
    if (systemConfig.serverInfo.attachment_storage_type==2) {
        //外网
        let option={
            cid:parseInt(cid),
            file_id:msg.file_id,
            file_name:msg.file_name,
            src_path:msg.url,
            dst_path: cid + '/' + user.uid + '/' + msg.file_id
        }
        window.CWorkstationCommunicationMng.requestUploadAttachmentToOSS(option)
    }else{
        //内网
        let mimeType='image/png';
        if (msg.msg_type==systemConfig.msg_type.Video) {
            mimeType='video/avi';
        }
        let server_type=systemConfig.server_type;
        let server_url=server_type.protocol+server_type.host + server_type.port+'/upload';
        window.CWorkstationCommunicationMng.HttpUploadTarget({
            file_path:msg.url,
            file_id:msg.file_id,
            file_cid:parseInt(cid),
            file_uid:parseInt(user.uid),
            file_type:msg.msg_type,
            file_name:msg.file_name,
            mimeType:mimeType,
            remote_url:server_url
        })
    }
}
export function sendTransferExam(cid){
    const systemConfig = store.state.systemConfig
    let examObj=root.transferExamQueue[cid];
    let list=examObj.ImageList;
    let count=0;
    for(let exam of list){
        let image_list=exam.image_list;
        count+=1;
        for(let image of image_list){
            // if (this.$root.transferExamCidObj[image.img_id]) {
            //     Toast(image.img_id+'仍未上传完成，无法重复上传')
            //     continue;
            // }
            let shotImgIdArr=image.img_id.split("\\")
            let shotImgId=shotImgIdArr[shotImgIdArr.length-1];
            shotImgId=shotImgId.split(".")[0]
            root.transferExamCidObj[shotImgId]={
                cid:cid,
                file_id:image.img_id+'-'+ new Date().valueOf()+'-'+count,
                img_id:image.img_id,
                exam_id:exam.exam_id,
                msg_type:image.msg_type,
                consultation_file_storage_type:systemConfig.serverInfo.consultation_file_storage_type,
                photometricInterpretation:image.photometricInterpretation
            };
        }
    }
    window.CWorkstationCommunicationMng.addExamToMobile(examObj);
    console.log('after addExamToMobile')
    //启动转发后删除暂存队列
    delete root.transferExamQueue[cid];
    console.log('after delete transferExamQueue')
}
export function openVisitingCard(data,type){
    const cid = data.group_id||data.groupid;
    const conversation = store.state.conversationList[cid]
    //type:1 聊天记录点击消息头像进入
    //type:2 好友列表点击头像进入 或 扫码加好友进入
    //type:3 群设置点击头像进入
    //type:4 所有群成员页面点击头像进入
    //type:5 点击加入群聊系统提示被邀请人名称进入
    //type:6 点击加入群聊系统提示邀请人名称进入
    //type:7 点击查找好友进入
    let user={};
    let target=''
    if (type==1) {
        let userid=data.sender_id
        user=conversation.attendeeList['attendee_'+userid]
    }else if(type==2){
        user=Object.assign({},data);
        user.userid=data.id;
    }else if(type==3){
        let userid=data.userid
        user=conversation.attendeeList['attendee_'+userid]
    }else if(type==4){
        let userid=data.userid
        user=conversation.attendeeList['attendee_'+userid]
    }else if(type==5){
        let userid=data.attendee_changed_info.user_id
        user=conversation.attendeeList['attendee_'+userid]
    }else if(type==6){
        let userid=data.attendee_changed_info.inviter_id || data.attendee_changed_info.qr_code_owner_id || data.attendee_changed_info.sharer_id
        user=conversation.attendeeList['attendee_'+userid]
    }else if (type==7) {
        user=data;
        for(let friend of window.vm.$store.state.friendList.list){
            if (user.id===friend.id) {
                user=friend;
                break;
            }
        }
    }
    target = window.vm.$route.path+'/visiting_card';
    store.commit('relationship/updatePersonalObj',user);
    console.log('openVisitingCard2',target)
    window.vm.$router.push(target);
}
export function pushImageToList(message,ignoreConsultationImages){
    //图片或视频信息放入总图像列表和会话图像列表
    const systemConfig = store.state.systemConfig
    const cid = message.group_id
    const conversation = store.state.conversationList[cid]
    if(!conversation){
        return
    }
    if(message.msg_type === systemConfig.msg_type.WITHDRAW){
        return
    }
    if (message.msg_type==systemConfig.msg_type.EXAM_IMAGES) {
        message=cloneDeep(message);
        message.msg_type=message.cover_msg_type;
    }
    if (message.msg_type==systemConfig.msg_type.Image
        ||message.msg_type==systemConfig.msg_type.Frame||
        message.msg_type==systemConfig.msg_type.OBAI||
        message.msg_type==systemConfig.msg_type.Cine||
        message.msg_type==systemConfig.msg_type.Video||
        message.msg_type==systemConfig.msg_type.RealTimeVideoReview||
        message.msg_type==systemConfig.msg_type.VIDEO_CLIP) {
        message.url_local=getThumbnailLocalImgUrl(message.url,message.msg_type)
        let conversationImages=conversation.galleryObj.gallery_list
        let consultationImages=store.state.consultationImageList.list;
        let isRepeatConversation=false
        let isRepeatConsultation=false
        if (ignoreConsultationImages){
            //AI分析图片不放入总图像列表里
            isRepeatConsultation=true;
        }
        for(let img of conversationImages){
            if (img.resource_id==message.resource_id) {
                isRepeatConversation=true;
                break;
            }
        }
        for(let img of consultationImages){
            if (img.resource_id==message.resource_id) {
                isRepeatConsultation=true;
                break;
            }
        }
        setTimeout(function(){
            //延时放入图像列表，否则当会话界面打开且收到图片消息时，同时更新src会出现问题
            if(!isRepeatConsultation){
                //图片在图像列表中未存在
                store.commit('consultationImageList/addFileToConsultationImages',message)
            }
            if (!isRepeatConversation) {
                //在会话的图像列表中未存在
                store.commit('conversationList/addFileToConversation',{
                    message:message,
                    cid:message.group_id
                });
                let commentObj={}
                commentObj={
                    ai_analyze_report:message.ai_analyze_report||{},
                    tag_names:message.tag_names,
                    comment_list:message.comment_list||[],
                    tags_list:message.tags_list||[]
                }
                store.commit('gallery/updateCommentToGallery',{
                    obj:commentObj,
                    resource_id:message.resource_id
                });
            }
        },0)
    }
}
export function getFileIcon(file_name){
    let file_type=file_name.replace(/.+\./,"").toLowerCase()
    return `static/resource/images/file_icon/${file_type}.png`
}
export function noFileIcon(event){
    event.currentTarget.src='static/resource/images/file_icon/temp.png'
}
const DownLoadImgFn =  {
    downLoadImgToLocal(img,type,index, callback,forceDownload){
        const systemConfig = store.state.systemConfig
        let imgObj=img//Object.assign({},img);
        if(!imgObj){
            console.log("[Error]downLoadImgToLocal imgObj error#######################")
            callback && callback();
            return;
        }
        index = index ? index : 0;
        imgObj.index=index;
        var imgListObj = {"imgObj":imgObj,"type":type,"index":index};
        //console.log("imgListObj:",imgListObj)
        //type:0 仅下载图片
        //type:1 consultationImageList缩略图下载异常
        //type:2 consultationImageList 原图 new Image下载异常
        //type:3 message头像下载异常
        //type:4 message缩略图下载异常
        //type:5 firend头像下载异常
        //type:6 chatList头像下载异常
        //type:7 friendApply头像下载异常
        //type:8 group_setting缩略图下载异常
        //type:9 group_setting头像下载异常
        //type:10 minePage头像下载异常
        //type:11 addFriend头像下载异常
        //type:12 云收藏缩略图下载异常
        //type:14 检查视图下载异常
        //type:15 资料库图片下载异常
        //type:16 资料库视频封面帧下载异常
        //type:17 手机控制盒子图片下载异常
        //type:18 个人名片头像下载异常
        //type:19 AI分析图片下载异常
        //type:20 检查列表头像下载异常
        //type:21 群列表头像下载异常
        //type:22 编辑群落页头像下载异常
        //type:23 扫码后的群卡片图像下载异常
        //type:24 群二维码头像下载异常
        //type:25 个人二维码头像下载异常
        //type:26 群落设置的群落头像下载
        //type:27 搜索页头像下载异常
        //type:28 发起语音头像下载异常
        //type:29 群落管理员列表头像下载异常

        var img_src = getImgObjUrl(imgObj, type);
        if(!img_src){
            console.log("[Error]downLoadImgToLocal img_src error#######################")
            console.log(imgObj)
            setImgObjUrl(imgObj, type, "static/resource/images/slt_err.png");
            callback && callback();
            return;
        }
        //下载到app本地
        let isApp = systemConfig.client_type.AppMobile == window.clientType || systemConfig.client_type.UltraSoundMobile == window.clientType
        const noDownloadImage = window.vm.$store.state.globalParams.noDownloadImage
        if((isApp&&!noDownloadImage)||forceDownload) {//mobile app.

            setImgObjUrl(imgObj, type,'static/resource/images/placeholder.png');
            setTimeout(function(){
                //延时下载，不阻塞UI主线程
                if(systemConfig.downLoad.DownQueue[img_src]){
                    if(systemConfig.downLoad.DownQueue[img_src] == 2){ //下载完
                        console.log("DownQueue Success 2 ---->", img_src);
                        var img_src_http = getLocalImgUrl(img_src);
                        //img.src = img_src_http;
                        //img.setAttribute("down_flag", 0);
                        setImgObjUrl(imgObj, type, img_src_http);
                    }else if(systemConfig.downLoad.DownQueue[img_src] == 1){ //正在下载
                        console.log("DownQueue downing 1 ---->", img_src);
                        if(systemConfig.downLoad.DownLoadImgDuplication[img_src]){
                            systemConfig.downLoad.DownLoadImgDuplication[img_src].push(imgListObj);
                        }else{
                            systemConfig.downLoad.DownLoadImgDuplication[img_src] = [];
                            systemConfig.downLoad.DownLoadImgDuplication[img_src].push(imgListObj);
                        }
                        //重复下载提高优先级
                        changeDowLoadImgPriority([imgObj.url]);
                    }else if(systemConfig.downLoad.DownQueue[img_src] == 3){ //下载失败
                        console.log("DownQueue downing fail ---->", img_src);
                        setImgObjUrl(imgObj, type, "static/resource/images/slt_err.png");
                    }
                    //开启下一个任务
                    if(systemConfig.downLoad.Queue.length > 0){
                        let taskIMg = systemConfig.downLoad.Queue.pop();
                        DownLoadImgFn.downLoadImgToLocal(taskIMg.imgObj,taskIMg.type,taskIMg.index, callback);
                    }
                    callback && callback();
                    return;
                }
                if (systemConfig.downLoad.CurrentTaskCount >= systemConfig.downLoad.MaxTaskCount) {
                    systemConfig.downLoad.Queue.push(imgListObj);
                    callback && callback();
                    return;
                }
                systemConfig.downLoad.CurrentTaskCount++;

                DownLoadImgFn.dowLoadImgToLocal_start(imgObj,type, callback,forceDownload);
            },200)

        }else{
            let target=getThumb(imgObj.msg_type);
            console.log(11,imgObj,target,'target')
            if (target&&imgObj.url_local&&imgObj.url_local.indexOf(target)>-1) {
                //加载大缩略图失败
                let originThumb=getLocalImgUrl(imgObj.url)
                let realUrl=getRealUrl(imgObj).serverRealUrl
                let isConsultation=false;
                if (imgObj.msg_type==systemConfig.msg_type.OBAI||imgObj.msg_type==systemConfig.msg_type.Frame||imgObj.msg_type==systemConfig.msg_type.Cine) {
                    isConsultation=true;
                }
                let data={
                    msg_type:imgObj.msg_type,
                    file_storage_type:systemConfig.serverInfo.attachment_storage_type,
                    url:imgObj.url_local,
                    original_url:realUrl,
                    isConsultation:isConsultation
                }
                root.socket.emit("request_get_thumbnail",data,(is_succ,json)=>{
                    console.log('request_get_thumbnail',is_succ)
                    if (is_succ) {
                        let image=new Image();
                        image.onload=()=>{
                            setImgObjUrl(imgObj, type, data.url);
                        }
                        image.onerror=()=>{
                            image.src=''
                            setTimeout(()=>{
                                image.src=data.url
                            },500)
                        }
                        let url = data.url
                        if(window.vm.$store.state.systemConfig.serverInfo.network_environment === 1){
                            url = Tool.replaceInternalNetworkEnvImageHost(url)
                        }
                        image.src=url
                    }
                });
                setImgObjUrl(imgObj, type, originThumb);
            }else{
                setImgObjUrl(imgObj, type, "static/resource/images/slt_err.png");
            }
            callback && callback();
        }
    },
    dowLoadImgToLocal_start(imgObj,type, callback,forceDownload) {
        const user = store.state.user
        const systemConfig = store.state.systemConfig
        var img_src= getImgObjUrl(imgObj, type);
        let isApp = systemConfig.client_type.AppMobile == window.clientType || systemConfig.client_type.UltraSoundMobile == window.clientType
        const noDownloadImage = window.vm.$store.state.globalParams.noDownloadImage
        if((isApp &&!noDownloadImage)||forceDownload){//mobile app.
            systemConfig.downLoad.DownQueue[img_src] = 1;
            var local_path = img_src;
            if (img_src.match(/^http:\/\//i) || img_src.match(/^https:\/\//i)){//单帧会诊文件
                var img_srcArr = img_src.split("mindray.com:443/");
                if(img_srcArr.length < 2){
                    img_srcArr = img_src.split("aliyuncs.com/");
                }
                if(img_srcArr.length < 2){
                    img_srcArr = img_src.split("mindray.com:8081/");
                }
                if(img_srcArr.length < 2){
                    img_srcArr = img_src.split("mindray.com:80/");
                }
                if(img_srcArr.length < 2){
                    img_srcArr = img_src.split("mindray.com/");
                }
                local_path = img_srcArr.pop();
            }
            //创建下载任务到临时目录
            var sd_path_cell1 = '_documents/Ruitong/'+systemConfig.server_type.host+'/'+user.uid+'/images/'+local_path + '.im_tmp';
            var sd_path_cell2 = '';
            let that = this
            if( true ){
                try {
                    Tool.createCWorkstationCommunicationMng({
                        name: "convertLocalFileSystemURL",
                        emitName: 'NotifyConvertedLocalFileSystemURL',
                        params:{url: sd_path_cell1},
                        timeout:1500
                    }).then((res)=>{
                        sd_path_cell2 = res.url
                        //与app本地apache站点一致
                        var sd_path = sd_path_cell2;
                        let serverResourceUrl = getFullServerResourceUrl(img_src)
                        var url = encodeURI(serverResourceUrl.full_url);
                        console.log("download url:" + url)
                        // console.log("sd_path: " + sd_path);
                        //解决ios的网络缓存问题
                        //url = CMainScreenUI.changImgUrlTypeWithRandomKey(url);
                        var t1 = new Date();
                        let t1_str=t1.getHours()+':'+t1.getMinutes()+':'+t1.getSeconds()+':'+t1.getMilliseconds()
                        console.info('启动下载:',local_path,t1_str)
                        Tool.createCWorkstationCommunicationMng({
                            name: "createDownload",
                            emitName: 'NotifyCreateDownload',
                            params:{
                                url:url,
                                options:{
                                    "filename": sd_path,
                                    "timeout": "5",
                                    "retry": "1",
                                    "retryInterval": "1"
                                }
                            },
                            timeout: null
                        }).then((downloadRes)=>{
                            console.log("[event] callback in plus.downloader.createDownload");
                            console.log(downloadRes);
                            console.log(status);

                            if(downloadRes.error_code == '0') {
                                var t2 = new Date();
                                // console.log("************************* 1 download ", t2-t1);
                                // console.log("CWorkstationCommunicationMng.createDownload200", d.filename);
                                var pathArr = sd_path.split("/");
                                var fileName = pathArr.pop();
                                fileName = fileName.replace(/\.im_tmp/, '');
                                var fielPath = pathArr.join("/") + '/';

                                console.log("[event] callback in plus.downloader.createDownload");
                                try {
                                    Tool.createCWorkstationCommunicationMng({
                                        name:'resolveLocalFileSystemURL',
                                        emitName:'NotifyResolvedLocalFileSystemURL',
                                        params:{action: 'moveTo', local_url: sd_path,remote_url:fielPath+'/'+fileName},
                                        timeout:2500,
                                    }).then((res)=>{
                                        if (res.error_code == '0') {
                                            var t3 = new Date();
                                            // console.log("************************* 2 move ", t3-t2);
                                            // console.log("MoveTo File success");
                                            console.info('下载完成:',local_path,' 大小:',parseInt(downloadRes.totalSize/1024)+'KB',' 下载耗时:',t2-t1,' 保存耗时',t3-t2)
                                            systemConfig.downLoad.DownQueue[img_src] = 2;
                                            //更新图片
                                            //var img_src2 = img.getAttribute("data-http-src");
                                            var img_src_http = getLocalImgUrl(img_src);
                                            //img.src = img_src_http;
                                            //img.setAttribute("down_flag", 0);

                                            setImgObjUrl(imgObj, type, img_src_http);
                                            //更新在重复列表中的图片
                                            // console.error('**************',!!systemConfig.downLoad.DownLoadImgDuplication[img_src])
                                            if(systemConfig.downLoad.DownLoadImgDuplication[img_src]){
                                                var len_d = systemConfig.downLoad.DownLoadImgDuplication[img_src].length;
                                                // console.log("DownLoadImgDuplication.update----->", img_src);
                                                // console.error('**************',len_d)
                                                for(let d=0;d<len_d;d++){
                                                    // console.error('**************',d)
                                                    var img_d = systemConfig.downLoad.DownLoadImgDuplication[img_src][d];
                                                    var img_src2= getImgObjUrl(img_d.imgObj, img_d.type);
                                                    var img_src_http2 = getLocalImgUrl(img_src2);
                                                    //img_d.src = img_src_http2;
                                                    //img_d.setAttribute("down_flag", 0);
                                                    // console.log("DownLoadImgDuplication.update----->", img_d);
                                                    setImgObjUrl(img_d.imgObj, img_d.type, img_src_http2);
                                                }
                                            }
                                            doSaveDownload(imgObj,img_src_http,type);
                                            callback && callback();
                                        } else {
                                            console.log("CreateDownload Error by File: " + res.error_message);
                                            setImgObjUrl(imgObj, type, "static/resource/images/slt_err.png");
                                            systemConfig.downLoad.DownQueue[img_src] = 3;
                                            delete systemConfig.downLoad.DownQueue[img_src];
                                            delete systemConfig.downLoad.DownLoadImgDuplication[img_src];

                                        }
                                    })
                                } catch (error) {
                                    Toast(error);
                                }
                            } else {
                                callback && callback();
                                // console.log("CWorkstationCommunicationMng.createDownload404", url);
                                console.info('下载失败:',local_path)
                                let target=getThumb(imgObj.msg_type);
                                let url_local=imgObj.url_local
                                if (target&&url_local&&url_local.indexOf(target)>-1) {
                                    //加载大缩略图失败
                                    let originThumb=getLocalImgUrl(imgObj.url)
                                    let realUrl=getRealUrl(imgObj).serverRealUrl
                                    let isConsultation=false;
                                    if (imgObj.msg_type==systemConfig.msg_type.OBAI||imgObj.msg_type==systemConfig.msg_type.Frame||imgObj.msg_type==systemConfig.msg_type.Cine) {
                                        isConsultation=true;
                                    }
                                    let data={
                                        msg_type:imgObj.msg_type,
                                        file_storage_type:systemConfig.serverInfo.attachment_storage_type,
                                        url:serverResourceUrl.abs_url,
                                        original_url:realUrl,
                                        isConsultation:isConsultation
                                    }

                                    root.socket.emit("request_get_thumbnail",data,(is_succ,json)=>{
                                        systemConfig.downLoad.DownQueue[img_src]=null;
                                        if (is_succ) {
                                            setImgObjUrl(imgObj,type,url_local)
                                        }else{
                                            setImgObjUrl(imgObj, type, originThumb);
                                            console.log('request_get_thumbnail_error',data)
                                        }
                                    });
                                    // setImgObjUrl(imgObj, type, originThumb);
                                }else{
                                    setImgObjUrl(imgObj, type, "static/resource/images/slt_err.png");
                                    systemConfig.downLoad.DownQueue[img_src] = 3;
                                    if(systemConfig.downLoad.DownLoadImgDuplication[img_src]){
                                        var len_d = systemConfig.downLoad.DownLoadImgDuplication[img_src].length;
                                        for(let d=0;d<len_d;d++){
                                            var img_d = systemConfig.downLoad.DownLoadImgDuplication[img_src][d];
                                            setImgObjUrl(img_d.imgObj, img_d.type, "static/resource/images/slt_err.png");
                                        }
                                    }
                                }
                            }
                            systemConfig.downLoad.CurrentTaskCount--;

                            //开启下一个任务
                            if(systemConfig.downLoad.Queue.length > 0){
                                let taskIMg = systemConfig.downLoad.Queue.pop();
                                DownLoadImgFn.downLoadImgToLocal(taskIMg.imgObj,taskIMg.type,taskIMg.index, callback);
                            }
                        })
                    })
                }catch(error){
                    Toast(error);
                }
            }
        }else{
            setImgObjUrl(imgObj, type, "static/resource/images/slt_err.png");
            callback && callback();
        }
    }
}
export function downLoadImgToLocal(img,type,index, callback,forceDownload){
    DownLoadImgFn.downLoadImgToLocal(img,type,index, callback,forceDownload)
}
export function dowLoadImgToLocal_start(img,type,index, callback,forceDownload){
    DownLoadImgFn.dowLoadImgToLocal_start(img,type,index, callback,forceDownload)
}
export function getShowDes(name){
    if(name.length > 8){
        name = name.substring(0, 8) + '......'
    }
    return name;
}
export function handleAfterLogin(user){
    window.localStorage.setItem('loginToken',user.new_token)
    window.localStorage.setItem('account',user.username)
    window.localStorage.setItem('uid',user.uid)
    window.localStorage.setItem('password','')
    store.commit('user/updateUser', user);
    window.CWorkstationCommunicationMng.CallAccountInfo(user)
    newMainDB(user.uid)
    // updateWebTracingConfig({userUuid:user.uid})

    // 初始化权限管理器
    if (user && user.uid) {
        console.log('移动端用户登录成功，初始化权限管理器:', user.uid, user.role);
        permissionManager.initialize(user).then(() => {
            console.log('移动端权限管理器初始化成功');
        }).catch(error => {
            console.error('移动端权限管理器初始化失败:', error);
        });
    }

    const probationary_expiry = user.probationary_expiry;
    const referralCode = store.state.globalParams.functionsStatus.referralCode;
    if (referralCode && probationary_expiry && 0 < probationary_expiry.length) {
        const tip =  formatString(i18n.t('nls_probationary_expiry_tip'), {
            1:probationary_expiry
        });

        Tool.openMobileDialog({
            message: tip
        })
    }

}

export function patientDesensitization(list=[]){
    const user = store.state.user
    const isDesensitization=user.preferences&&user.preferences.is_desensitization||0;
    if (isDesensitization==0) {
        //不脱敏直接返回
        return list;
    }
    list.forEach(image=>{
        if (image.patient_name) {
            image.patient_name='***'
        }
    })
    return list;
}
export function parseSingleChat(list){
    const systemConfig = store.state.systemConfig
    let arr=[];
    list.forEach(item =>{
        if (item.type==systemConfig.ConversationConfig.type.Group) {
            arr.push(item)
        }else if (item.type==systemConfig.ConversationConfig.type.Single) {
            const userInfo=item.attendeeList[0].userInfo;
            item.avatar=userInfo.avatar
            item.subject=userInfo.nickname
            item.role=userInfo.role
            item.sex=userInfo.sex
            item.fid=userInfo.id;
            arr.push(item)
        }
    })
    return arr;
}
export function getLocalAvatar(item){
    const systemConfig = store.state.systemConfig
    let avatar_local='';
    if(item.is_single_chat==1||item.nickname){
        if (item.service_type==systemConfig.ServiceConfig.type.FileTransferAssistant) {
            avatar_local='static/resource/images/transfer.png'
        }else{
            //单聊
            const userStatus=item.user_status||item.status
            if (userStatus===systemConfig.userStatus.Destroy) {
                avatar_local='static/resource/images/destroy.png'
                //item.sex=2;todo 应该在别的地方实现
            }else if (!item.avatar||/user\/avatar\/default\/0\.png/.test(item.avatar)||/static\/resource\/images\//.test(item.avatar)) {
                if(item.sex==1){
                    avatar_local='static/resource/images/b3-1.png'
                }else{
                    avatar_local='static/resource/images/b2-1.png'
                }
            }else{
                //给出默认头像
                avatar_local='';
            }
        }
    }else{
        if (item.type==3) {
            if (!item.avatar) {
                avatar_local='static/resource/images/groupset.png'
            }
        }else if (!item.avatar) {
            avatar_local='static/resource/images/b1.png'
        }
    }
    // const avatar=addRootToUrl(item.avatar);
    if (avatar_local==='') {
        avatar_local=getLocalImgUrl(item.avatar);
    }else{
        avatar_local=addRootToUrl(avatar_local);
    }
    return avatar_local
}
/**
 * 使用 CryptoJS 进行 AES-256-CBC 解密
 * @param {string} encData - 加密后的数据（Base64 编码）
 * @param {string} password - 密码
 * @param {string} iv - 初始化向量
 * @returns {string} 解密后的字符串
 */
export function desEncAes256(encData, password, iv) {
    try {
        // 将密码和IV转换为WordArray
        const key = CryptoJS.enc.Utf8.parse(password);
        const ivWordArray = CryptoJS.enc.Utf8.parse(iv);

        // 解密
        const decrypted = CryptoJS.AES.decrypt(encData, key, {
            iv: ivWordArray,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        });

        // 转换为UTF-8字符串
        return decrypted.toString(CryptoJS.enc.Utf8);
    } catch (e) {
        console.log(`解密错误${JSON.stringify({ encData, password, iv })}`);
        throw new Error(`数据解密失败: ${e.message}`);
    }
}

/**
* 解析服务器返回的信息并解密
* @param {Object} json - 服务器返回的JSON对象，包含encStr字段
* @returns {Object} 解密后的数据对象
*/
export function parseServerInfo(json) {
    let str = json.encStr;
    const password = str.slice(-32); // 密码
    const iv = password.slice(0, 16); // 向量
    const data = JSON.parse(desEncAes256(str.slice(0, -32), password, iv));
    return data;
    // console.log(data); // 前端解密后的数据
}

export function getDateDiff(start, end){
    let startTime = moment(start).toDate().getTime();
    let endTime = moment(end).toDate().getTime();
    let diffTime = endTime - startTime;

    let diffDay = Math.floor(diffTime / (24 * 3600 * 1000)); //相差天数
    let lev1 = diffTime % (24 * 3600 * 1000);
    let diffHour = Math.floor(lev1 / (3600 * 1000)); //相差小时
    let lev2 = lev1 % (3600 * 1000);
    let diffMin = Math.floor(lev2 / (60 * 1000));
    let lev3 = lev2 % (60 * 1000);
    let diffSecond = String(Math.floor(lev3 / 1000));

    let allHours = diffDay * 24 + diffHour;
    let Str = `${diffSecond}${i18n.t('live_replay_second')}`
    if(diffMin>0){
        Str = `${diffMin}${i18n.t('live_replay_minute')}${Str}`
    }
    if(allHours>0){
        Str = `${allHours}${i18n.t('live_replay_hour')}${diffMin}${i18n.t('live_replay_minute')}`
    }

    return Str;
}
export function judgeIfCurrentYear(start, end){
    const currentYear = moment(start).toDate().getFullYear()
    const targetYear = moment(end).toDate().getFullYear()
    return currentYear === targetYear
}

export function getDefaultPreferences(user){
    let default_pre={
        auto_upload:'0',
        default_conversation:{}
    }
    let preferences = user.preferences;
    if (typeof preferences =='string') {
        preferences=JSON.parse(preferences);
        if (preferences.default_conversation) {
            preferences.default_conversation=JSON.parse(preferences.default_conversation)
        }
    }
    if (preferences) {
        Object.keys(preferences).forEach((key)=>{
            default_pre[key]=preferences[key];
        })
    }
    return default_pre;
}
let focusTimer
export function handleInputFocusForIOS(event){
    var e=event.target// this.$refs.mobile_number
    if(window.browse_type.includes("IOS")){
        e.readOnly = true
        if(focusTimer){
            clearTimeout(focusTimer)
            focusTimer = null
        }
        focusTimer = setTimeout(()=>{
            e.readOnly = false
            e.focus()
        },100)
    }
}
export function getLiveRoomObj(oCid){
    const cid = oCid||window.vm.$route.params.cid
    let liveRoom = null
    try {
        if(Tool.checkAppClient('Browser')){
            liveRoom = window.CLiveRoomWeb&&window.CLiveRoomWeb[cid]
        }else{
            liveRoom = window.CLiveRoom&&window.CLiveRoom[cid]
        }
    } catch (error) {
        console.error('getLiveRoomObj',error)
        liveRoom = null
    }
    return liveRoom
}

export function destroyAllConference(){
    window.CWorkstationCommunicationMng.forceLeaveChannel()
    if(window.main_screen){
        if(window.main_screen.CMonitorWallPush&&window.main_screen.CMonitorWallPush.joined){
            window.main_screen.CMonitorWallPush.LeaveChannelSilence()
        }
        if(window.vm.$root.currentLiveCid&&window.main_screen.conversation_list[window.vm.$root.currentLiveCid]){
            let liveRoom = getLiveRoomObj()
            liveRoom.LeaveChannelAux('normal')
        }
    }
}
export function jumpRoute(level,path){
    window.directPath=path
    back(level);
}
export function getRecordSubject(message){
    if(message.live_record_data && message.live_record_data.subject){
        return message.live_record_data.subject
    }else if (message.live_record_data && message.live_record_data.creator_name) {
        return `${message.live_record_data.creator_name}${i18n.t('initiated_live_broadcast')}`;
    }else{
        return ''
    }
}
export function formatDurationTime(duration){
    let start_time = new Date().getTime() - (duration || 0)*1000
    let millisecond = new Date() - start_time
    let h = Math.floor(millisecond / (60 * 60 * 1000))
    h = h < 10 ? '0' + h : h
    let min = Math.floor((millisecond % (60 * 60 * 1000)) / (60 * 1000))
    min = min < 10 ? '0' + min : min
    let sec = Math.floor(((millisecond % (60 * 60 * 1000)) % (60 * 1000)) / 1000)
    sec = sec < 10 ? '0' + sec : sec
    let Str = `${sec}${i18n.t('live_replay_second')}`
    if(min>0){
        Str = `${min}${i18n.t('live_replay_minute')}${Str}`
    }
    if(h>0){
        Str = `${h}${i18n.t('live_replay_hour')}${min}${i18n.t('live_replay_minute')}`
    }
    return Str;
    // _this.count_time = h + ':' + min + ':' + sec
}
export function getClipSubject(message){
    return `${message.nickname}${i18n.t('generated_video_clips')}`
}
export function getReviewVideoSubject(message){
    if (message && message.live_record_data && message.live_record_data.creator_name) {
        return `${message.live_record_data.creator_name}${i18n.t('initiated_live_broadcast')}`;
    }
}

export function generateGalleryFileId(galleryItem){
    return galleryItem.resource_id + galleryItem.file_id + galleryItem.gmsg_id
}

export function getResourceTempStatus(resource_id,key){
    const storeState = window.vm&&window.vm.$store&&window.vm.$store.state
    const resourceTempStatus = storeState.resourceTempStatus;
    if (resourceTempStatus.hasOwnProperty(resource_id)) {
        if (resourceTempStatus[resource_id].hasOwnProperty(key)) {
            return resourceTempStatus[resource_id][key]
        }else{
            return null
        }
    }else{
        return null
    }
}
export function checkResourceType(currentFile){
    const systemConfig = store.state.systemConfig
    if(currentFile.msg_type==systemConfig.msg_type.Image||
    currentFile.img_type_ex==systemConfig.msg_type.Image||
    currentFile.img_type_ex==systemConfig.msg_type.Frame||
    currentFile.msg_type==systemConfig.msg_type.Frame   ||
    currentFile.img_type_ex==systemConfig.msg_type.OBAI ||
    currentFile.msg_type==systemConfig.msg_type.OBAI){
        return 'image'
    }else if(currentFile.msg_type==systemConfig.msg_type.Cine||
    currentFile.img_type_ex==systemConfig.msg_type.Cine||
    currentFile.msg_type==systemConfig.msg_type.Video||
    currentFile.img_type_ex==systemConfig.msg_type.Video){
        return 'video'
    }else if(currentFile.msg_type==systemConfig.msg_type.RealTimeVideoReview||
    currentFile.img_type_ex==systemConfig.msg_type.RealTimeVideoReview||
    currentFile.img_type_ex==systemConfig.msg_type.VIDEO_CLIP||
    currentFile.msg_type==systemConfig.msg_type.VIDEO_CLIP){
        return 'review_video'
    }
}
export function resetGlobalCustomWindowObject(){
    window.main_screen=undefined
    window.CLiveRoomWeb=undefined
    window.CLiveRoom=undefined
    window.agoraClient={}
    window.CReverseControl={}
    window.livingStatus = 0
}
export function resetLoginStorage(){
    window.localStorage.setItem('account','')
    window.localStorage.setItem('password','')
    window.localStorage.setItem('loginToken','')
    window.localStorage.setItem('uid','')
    window.localStorage.removeItem('local_store_device_token');
}
export function checkIsCreator(cid){
    const conversation = store.state.conversationList[cid];
    const user = store.state.user
    return conversation&&(user.uid==conversation.creator_id)&&(conversation.is_single_chat==0)
}
export function checkIsManager(cid){
    const conversation = store.state.conversationList[cid];
    const systemConfig = store.state.systemConfig
    const user = store.state.user
    let isManager = false;
    if (conversation) {
        for(let key in conversation.attendeeList){
            let item = conversation.attendeeList[key];
            if (item.role == systemConfig.groupRole.manager && item.userid == user.uid) {
                isManager = true;
                break;
            }
        }
    }
    return isManager;
}
export function getMoreResourceList(resource_id,cid){
    const systemConfig = store.state.systemConfig
    const msg_type = systemConfig.msg_type
    const unLoadResourceTypeArr = [
        msg_type.Sound,
        msg_type.WITHDRAW,
        msg_type.EXAM_IMAGES,
        msg_type.File,
        msg_type.AI_ANALYZE,
        msg_type.IWORKS_PROTOCOL
    ];
    const data = []
    return new Promise((resolve,reject)=>{
        window.main_screen.conversation_list[cid].getResourceList(
            {
                limit: systemConfig.consultationImageShowNum,
                type:'all',
                lastResourceID: resource_id,
            },
            (res) => {
                if (res.error_code === 0) {
                    res.data.forEach(item=>{
                        if(!unLoadResourceTypeArr.includes(item.msg_type)){
                            data.push(item)
                        }
                    })
                    resolve(data)
                } else {
                    resolve([])
                    console.error('getResourceList error 1')
                    // Toast("getResourceList error 1");
                }
            }
        );

    })
}
export function getMessageAiReportFromLocal(msg){
    let item = msg//JSON.parse(JSON.stringify(msg))
    if(!item||!item.resource_id){
        return null;
    }
    let  ai_analyze_report= null
    let storeItem = store.state.gallery.commentObj[item.resource_id]//JSON.parse(JSON.stringify(store.state.gallery.commentObj[item.resource_id]));
    const get_report= (item,key)=>{
        let temp = null
        if(storeItem&&storeItem.ai_analyze_report && storeItem.ai_analyze_report&&storeItem.ai_analyze_report[key]&&storeItem.ai_analyze_report[key][item.resource_id]){
            temp = storeItem.ai_analyze_report
        }
        if(item.ai_analyze_report && item.ai_analyze_report&& item.ai_analyze_report[key]&&item.ai_analyze_report[key][item.resource_id]){
            temp = item.ai_analyze_report
        }
        if(item.ai_analyze&&item.ai_analyze.status&&item.ai_analyze.report&&item.ai_analyze.report[key]&&item.ai_analyze.report[key][item.resource_id]){
            let ai_analyze = item.ai_analyze
            temp = {
                id:ai_analyze.ai_analyze_id,
                group_id:ai_analyze.group_id,
                sender_id:ai_analyze.sender_id,
                send_ts:ai_analyze.send_ts,
                type:ai_analyze.type,
                status:ai_analyze.status,
                error:ai_analyze.report.error,
            }
            temp = {...item.ai_analyze.report, ...temp}
        }
        return temp
    }
    ai_analyze_report = get_report(item, 'clips')
    if(!ai_analyze_report){
        ai_analyze_report = get_report(item,'mark_list')
    }
    return cloneDeep(ai_analyze_report);
}

export function imageStandardIcon(msg){
    let item = cloneDeep(msg)
    const functionsStatus = store.state.globalParams.functionsStatus;
    const cid = item.group_id||item.groupid;
    const convsrsation = store.state.conversationList[cid]
    const systemConfig = store.state.systemConfig

    let ai_analyze_report = getMessageAiReportFromLocal(item)
    let ai_analyze_type = ai_analyze_report?.type

    if(false&&functionsStatus.drAIAssistant && ai_analyze_type == store.state.aiPresetData.typeIndex.drChest){
        let result = {css:'dr_result_icon',type:'DrAiAnalyze',label:'',tips:''}
        if(ai_analyze_report){
            if(ai_analyze_report.error){
                return [result]
            }else{
                let clips=ai_analyze_report.clips
                if(clips&&clips[item.resource_id]){
                    let standard_desc = window.vm.$store.state.aiPresetData.DrViews.standard_desc
                    if('score' in clips[item.resource_id] ){
                        result.css = result.css + ' bg_blue'
                        result.label = i18n.t('level_b')

                        if(clips[item.resource_id].score>standard_desc.ai_height.lowest){
                            result.css = result.css + ' bg_blue'
                            result.label = i18n.t('level_a')
                        }
                        if(clips[item.resource_id].score<=standard_desc.ai_lower.highest){
                            result.css = result.css + ' bg_red'
                            result.label = i18n.t('level_c')
                        }
                    }
                }
            }
        }
        return [result]
    }

    if( functionsStatus.breastAI && ai_analyze_type && (item.msg_type == systemConfig.msg_type.AI_ANALYZE||item.ai_analyze_id||ai_analyze_report)
    ){
        // if(ai_analyze_type == store.state.aiPresetData.typeIndex.abdomen||ai_analyze_type == store.state.aiPresetData.typeIndex.cardiac){
        if(ai_analyze_type == store.state.aiPresetData.typeIndex.abdomen){
            let result = {css:'icon iconfont',type:'AiAnalyzeAbdomenCip',label:'',tips:''}
            if(ai_analyze_report){
                if(ai_analyze_report.error){
                    if(!item.url){
                        result.css = result.css + ' ai_result_deletion_icon icon-wenhao-yuankuang-copy'
                        result.tips = i18n.t('view_deletion')
                    }else{
                        // result.css = result.css + ' ai_result_deletion_icon icon-wenhao-yuankuang-copy'
                        result.tips = i18n.t('analyze_fail_tip')
                    }
                }
                let clips=ai_analyze_report.clips
                if(clips&&clips[item.resource_id]){
                    if('quality' in clips[item.resource_id][0] && clips[item.resource_id][0].quality<2){
                        result.css = result.css + '  qc_standard_icon icon-duihao-yuankuang'
                        if(clips[item.resource_id][0].quality<1){
                            result.tips = i18n.t('view_quality_text') + i18n.t('standard')
                        }else{
                            result.tips = i18n.t('view_quality_text') + i18n.t('basic_standard')
                        }
                    }else{
                        result.css = result.css + '  qc_non_standard_icon icon-gantanhao-yuankuang'
                        result.tips = i18n.t('view_quality_text') + i18n.t('non_standard')
                    }

                    if(clips[item.resource_id][0] && clips[item.resource_id][0].clip_id && clips[item.resource_id][0].clip_id.toLowerCase()=='undefined'){
                        result = {css:'icon iconfont',type:'AiAnalyzeAbdomenCip',label:'',tips:''}
                    }
                }else{
                    if(!item.url){
                        result.css = result.css + ' ai_result_deletion_icon icon-wenhao-yuankuang-copy'
                        result.tips = i18n.t('view_deletion')
                    }
                }
            }else{
                if(!item.url){
                    result.css = result.css + ' ai_result_deletion_icon icon-wenhao-yuankuang-copy'
                    result.tips = i18n.t('view_deletion')
                }
            }
            return [result]
        }
        if(ai_analyze_type == store.state.aiPresetData.typeIndex.obstetrical){
            let result = {css:'',type:'AiAnalyzeObstetrical',label:'',tips:''}
            if(ai_analyze_report&&ai_analyze_report.ai_analyze_id){
                let clips=ai_analyze_report.clips||{}
                const mc_resource_map = clips[item.resource_id]? clips[item.resource_id][0] : {}
                if( mc_resource_map
                && mc_resource_map.finshed
                && mc_resource_map.report
                ){
                    let ai_result = mc_resource_map.report
                    if(!ai_result.isSuccess){
                        result.css = result.css + 'error'
                    }
                    if(ai_result && 'view_quality' in ai_result && ai_result.view_type!=null&&ai_result.view_quality!=null){
                        if(ai_result.view_quality<2){
                            result.css = result.css + 'icon iconfont qc_standard_icon icon-duihao-yuankuang'
                            if(ai_result.view_quality<1){
                                result.tips = i18n.t('view_quality_text') + i18n.t('standard')
                            }else{
                                result.tips = i18n.t('view_quality_text') + i18n.t('basic_standard')
                            }
                        }else{
                            result.css = result.css + 'icon iconfont qc_non_standard_icon icon-gantanhao-yuankuang'
                            result.tips = i18n.t('view_quality_text') + i18n.t('non_standard')
                        }
                    }else{
                        result.css = result.css + 'error'
                    }
                }else{
                    result.css = result.css + 'error'
                }
            }else{
                result.css = result.css + 'error'
            }
            return [result]
        }
    }else{
        let result = {css:'',type:'AiAnalyzeObstetrical',label:'',tips:''}
        if( false&&functionsStatus.obstetricalAI
            &&item.mc_resource_map
            && item.mc_resource_map.ai_report
            && item.mc_resource_map.ai_report.finshed
            && item.mc_resource_map.ai_report.report
        ){
            let ai_result = item.mc_resource_map.ai_report.report
            if(!ai_result.isSuccess){
                result.css = result.css + 'error'
            }
            if(ai_result && 'view_quality' in ai_result&& item.mc_resource_map.type!=null&&item.mc_resource_map.quality!=null){
                if(ai_result.view_quality<2){
                    result.css = result.css + 'icon iconfont qc_standard_icon iconduihao-yuankuang'
                    if(ai_result.view_quality<1){
                        result.tips = i18n.t('view_quality_text') + i18n.t('standard')
                    }else{
                        result.tips = i18n.t('view_quality_text') + i18n.t('basic_standard')
                    }
                }else{
                    result.css = result.css + 'icon iconfont qc_non_standard_icon icongantanhao-yuankuang'
                    result.tips = i18n.t('view_quality_text') + i18n.t('non_standard')
                }
            }else{
                result.css = result.css + 'error'
            }
        }else{
            result.css = result.css + 'error'
        }
        return [result]
    }
}

export function parseObjToArr(data){
    var arr=[]
    for(let item in data){
        arr.push({...data[item],id:item})
    }
    return arr
}
export function filterDataByNickname(data, pattern) {
    // 使用正则表达式筛选数据
    const filteredData = data.filter((item) => {
        // 使用正则表达式进行匹配
        const regex = new RegExp(pattern, "ig");
        return  item.alias_name&&regex.test(item.alias_name)||item.remarkMapNickname&&regex.test(item.remarkMapNickname) || item.nickname&&regex.test(item.nickname);
    });
    return filteredData;
}
export function formatAttendeeNickname(attendeeList){
    let list = parseObjToArr(attendeeList);
    const remarkMap = window.vm.$store.state.friendList.remarkMap;
    let filterList = []; //后端把所有用户都返回回来，前端只显示未退群用户
    for (let i = 0; i < list.length; i++) {
        list[i].remarkMapNickname = remarkMap [list[i].userid];
        if (list[i].attendeeState != 0) {
            filterList.push(list[i]);
        }
        list[i].showNickname = list[i].alias_name || list[i].remarkMapNickname || list[i].nickname
    }
    return filterList;
}
export function formatAttendeeNicknameToMap(attendeeList){
    let list = parseObjToArr(attendeeList);
    const remarkMap = window.vm.$store.state.friendList.remarkMap;
    let filterObj = {}; // 使用对象存储数据，以 uid 为键，showNickname 为值
    for (let i = 0; i < list.length; i++) {
        list[i].remarkMapNickname = remarkMap[list[i].userid];
        if (list[i].attendeeState != 0) {
            filterObj[list[i].userid] = list[i].alias_name || list[i].remarkMapNickname || list[i].nickname;
        }
        list[i].showNickname = list[i].alias_name || list[i].remarkMapNickname || list[i].nickname;
    }
    return filterObj;
}
export function checkPrivacyPolicy(){ // 检测是否同意隐私策略
    const systemConfig = store.state.systemConfig
    let serverType = localStorage.getItem('serverType') || '云++'
    let privacyStatus = JSON.parse(localStorage.getItem('isAgreePrivacyPolicy')||"{}")
    console.log('privacyStatus',privacyStatus)
    // 从envConfig中获取版本号，如果没有则使用默认值
    let privacy_version = systemConfig.envConfig && systemConfig.envConfig.privacy_agreement_version

    // 获取用户已同意的版本号
    let agreedVersion = privacyStatus[serverType]
    console.log('agreedVersion',agreedVersion,privacy_version)
    // 是否同意过隐私协议
    let hasAgreed = !!(agreedVersion && agreedVersion !== 0)

    // 是否需要再次同意隐私协议
    let needReagree = false
    if (hasAgreed) {
        // 只要版本号不一致，就强制认为需要再次同意隐私协议
        needReagree = privacy_version && (String(agreedVersion) !== String(privacy_version))
    }

    return {
        hasAgreed: hasAgreed,        // 是否同意过
        needReagree: needReagree     // 是否需要再次同意
    }
}

export function checkPrivacyPolicyStrict(){ // 严格检测隐私策略（需要版本号匹配）
    const privacyCheck = checkPrivacyPolicy()
    // 严格模式：必须同意过且不需要重新同意
    return privacyCheck.hasAgreed && !privacyCheck.needReagree
}
export function getResourceTempState(resource_id) { //获取资源的状态
    const resourceTempStatus = window.vm.$store.state.resourceTempStatus;
    if (resourceTempStatus.hasOwnProperty(resource_id)) {
        if (resourceTempStatus[resource_id].hasOwnProperty("state")) {
            return resourceTempStatus[resource_id].state;
        } else {
            return 1;
        }
    } else {
        return 1;
    }
}
export function toFixedNumber(q, m=1){
    if(!q || q==undefined || q==null){
        return 0
    }
    q=parseFloat(q)
    if(q<100&&q>0){
        return parseFloat(q.toFixed(m))
    }else{
        return parseFloat(q.toFixed(0))
    }
}
export function goPrivacyPolicy(){
    const server_type = window.vm.$store.state.systemConfig.server_type;
    // 根据CN/CE环境判断隐私协议语言，而不是根据当前语言设置
    // CN环境使用中文隐私协议，CE环境使用英文隐私协议
    const env = process.env.VUE_APP_PROJECT_NOV || 'CN';
    let privacy_version = window.vm.$store.state.systemConfig.envConfig && window.vm.$store.state.systemConfig.envConfig.privacy_agreement_version
    let host = server_type.protocol + server_type.host + server_type.port;
    if (host.indexOf("localhost") > -1 || host.indexOf("192.168") > -1) {
        host = `https://${Tool.getHostConfig().dev}`;
    }
    const url = host + `/privacyPolicyPage/${env}/pravicyPolicy${env}_${privacy_version}.html`;
    if (Tool.checkAppClient('Browser')) {
        window.open(url, "blank");
    } else {
        Tool.openLinkByDefaultBrowser(url)
    }
}
