<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
        <title>验证码组件</title>
        <!-- vConsole for development -->
        <style>
            body {
                position: relative;
                margin: 0;
                padding: 0;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB",
                    "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
                background-color: transparent;
                /* 确保页面完全透明 */
                background: transparent;
            }

            .captcha-container {
                background: transparent;
                border-radius: 0;
                padding: 0;
                box-shadow: none;
                /* 隐藏容器，只有验证码弹窗时才显示 */
                display: none !important;
            }

            .captcha-title {
                font-size: 18px;
                font-weight: 600;
                color: #333;
                text-align: center;
                margin-bottom: 20px;
            }

            #captcha-element {
                width: 100%;
                min-height: 50px;
                margin-bottom: 20px;
            }

            #validate-button {
                width: 100%;
                height: 44px;
                background: #1890ff;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 16px;
                cursor: pointer;
                transition: background-color 0.3s;
            }

            #validate-button:hover {
                background: #40a9ff;
            }

            #validate-button:disabled {
                background: #d9d9d9;
                cursor: not-allowed;
            }

            .status-message {
                margin-top: 15px;
                padding: 10px;
                border-radius: 4px;
                text-align: center;
                font-size: 14px;
                display: none;
            }

            .status-success {
                background: #f6ffed;
                border: 1px solid #b7eb8f;
                color: #52c41a;
            }

            .status-error {
                background: #fff2f0;
                border: 1px solid #ffccc7;
                color: #ff4d4f;
            }

            .status-loading {
                background: #e6f7ff;
                border: 1px solid #91d5ff;
                color: #1890ff;
            }

            /* 全屏Loading样式 - 透明背景 */
            .fullscreen-loading {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: transparent;
                display: none;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                z-index: 9999;
                transition: opacity 0.3s ease-in-out;
                opacity: 0;
                /* 完全隐藏loading，保持页面透明 */
            }

            .loading-spinner {
                width: 50px;
                height: 50px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #1890ff;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin-bottom: 20px;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .loading-text {
                font-size: 16px;
                color: #666;
                text-align: center;
                line-height: 1.5;
            }

            .loading-dots {
                display: inline-block;
                width: 20px;
                text-align: left;
            }

            .loading-dots::after {
                content: '';
                animation: dots 1.5s steps(4, end) infinite;
            }

            @keyframes dots {
                0%, 20% { content: ''; }
                40% { content: '.'; }
                60% { content: '..'; }
                80%, 100% { content: '...'; }
            }

            /* 隐藏状态 */
            .fullscreen-loading.hidden {
                opacity: 0;
                pointer-events: none;
            }

            /* 隐藏阿里云验证码SDK自动生成的遮罩层 */
            .aliyunCaptcha-mask,
            #aliyunCaptcha-mask,
            [class*="aliyunCaptcha-mask"],
            [id*="aliyunCaptcha-mask"] {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                background: transparent !important;
            }

            /* 确保验证码弹窗本身可以正常显示，但移除背景遮罩 */
            .aliyunCaptcha-popup,
            #aliyunCaptcha-popup,
            [class*="aliyunCaptcha-popup"],
            [id*="aliyunCaptcha-popup"] {
                background: transparent !important;
                box-shadow: none !important;
            }

            /* 隐藏可能的其他遮罩元素 */
            .aliyun-captcha-mask,
            #aliyun-captcha-mask,
            .captcha-mask,
            #captcha-mask,
            #aliyunCaptcha-common-errorTip,
            [class*="captcha-mask"],
            [id*="captcha-mask"] {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                background: transparent !important;
            }
        </style>
    </head>

    <body>
        <!-- 隐藏的验证码容器，只有阿里云验证码弹窗会显示 -->
        <div id="captcha-element" style="display: none;"></div>
        <div id="button" class="login-btn" style="display: none;"></div>

        <script>
            // 全局变量
            var captcha = null;
            var isValidating = false;
            var currentLanguage = "cn"; // 默认中文
            var isDevelopment = false;
            var isError = false;

            // 状态管理
            var ValidationState = {
                IDLE: "idle",
                LOADING: "loading",
                SUCCESS: "success",
                ERROR: "error",
            };

            // 从地址栏获取参数配置
            function getUrlConfig() {
                var urlParams = new URLSearchParams(window.location.search);

                // 获取语言参数，支持 lang 和 language 两种参数名
                var langParam = urlParams.get("lang") || urlParams.get("language") || "CN";

                // 语言映射表：URL参数 -> 内部语言代码
                var languageMapping = {
                    CN: "cn",
                    EN: "en",
                    ES: "es",
                    PTBR: "ptbr",
                    PT: "ptbr", // 兼容简写
                    RU: "ru",
                };

                // 设置语言，默认为中文
                var detectedLanguage = languageMapping[langParam.toUpperCase()] || "cn";

                // 获取环境参数
                var envParam = urlParams.get("env") || urlParams.get("environment") || "production";
                var detectedEnv =
                    envParam.toLowerCase() === "development" ||
                    envParam.toLowerCase() === "dev" ||
                    envParam.toLowerCase() === "test";

                console.log("URL配置解析:", {
                    原始语言参数: langParam,
                    检测到的语言: detectedLanguage,
                    原始环境参数: envParam,
                    检测到的环境: detectedEnv ? "development" : "production",
                });

                return {
                    language: detectedLanguage,
                    isDevelopment: detectedEnv,
                };
            }

            // 显示状态消息 - 保持透明，只记录日志
            function showStatus(state, message) {
                console.log("状态更新:", state, message);
                // 不显示任何状态信息，保持页面透明
            }

            // 隐藏状态消息 - 保持透明
            function hideStatus() {
                // 不需要隐藏任何内容，页面本身就是透明的
            }

            // 显示全屏Loading - 保持透明，不显示任何内容
            function showFullscreenLoading(text) {
                // 不显示任何loading，保持页面透明
                console.log("Loading状态:", text || "准备中...");
            }

            // 隐藏全屏Loading - 保持透明
            function hideFullscreenLoading() {
                // 不需要隐藏任何内容，页面本身就是透明的
                console.log("Loading完成");
            }

            // 移除调试信息功能，保持页面完全透明

            // 环境检测和配置获取
            function getSceneConfig() {
                // 生产环境配置
                var productionConfig = {
                    SceneId: "s4yebkic",
                    prefix: "153y4u",
                };

                // 开发/测试环境配置
                var developmentConfig = {
                    SceneId: "1bqq236l1",
                    prefix: "153y4u",
                };

                return isDevelopment ? developmentConfig : productionConfig;
            }

            // 初始化验证码
            function initCaptcha() {
                return new Promise(function (resolve, reject) {
                    var initResolve = resolve;
                    var initReject = reject;

                    // 设置更长的超时时间（移动端网络考虑）
                    var timeout = setTimeout(function () {
                        console.error("❌ Captcha initialization timeout (考虑移动端网络延迟)");
                        if (initReject) {
                            initReject(new Error("Captcha initialization timeout - check network connection"));
                        }
                    }, 30000); // 从10秒增加到30秒

                    // 检查网络状态
                    if (!checkNetworkStatus()) {
                        clearTimeout(timeout);
                        reject(new Error("No network connection"));
                        return;
                    }

                    // 模仿Vue组件的SDK检查逻辑
                    if (!window.initAliyunCaptcha) {
                        console.error('下载无痕验证失败，重新下载');
                    }

                    // 首先确保SDK已加载
                    ensureSDKLoaded()
                        .then(function () {
                            try {
                                if (!window.initAliyunCaptcha) {
                                    clearTimeout(timeout);
                                    reject(new Error("AliyunCaptcha SDK not loaded"));
                                    return;
                                }

                                var config = getSceneConfig();

                                // 阿里云验证码SDK的语言映射
                                var aliyunLanguageMapping = {
                                    cn: "cn",
                                    en: "en",
                                    es: "es",
                                    ptbr: "pt",
                                    ru: "ru",
                                };

                                var useLang = aliyunLanguageMapping[currentLanguage] || "cn";

                                console.log("Initializing captcha with config:", config, "language:", useLang);

                                window.initAliyunCaptcha({
                                    SceneId: config.SceneId,
                                    prefix: config.prefix,
                                    mode: "popup",
                                    element: "#captcha-element",
                                    button: "#button",
                                    success: handleCaptchaSuccess,
                                    fail: handleCaptchaFail,
                                    getInstance: function (instance) {
                                        clearTimeout(timeout);
                                        captcha = instance;
                                        console.log("Captcha instance initialized successfully");
                                        if (initResolve) {
                                            initResolve();
                                            initResolve = null;
                                            initReject = null;
                                        }
                                    },
                                    slideStyle: {
                                        width: 360,
                                        height: 40,
                                    },
                                    language: useLang,
                                    onClose: handleCaptchaClose,
                                    onError: function (error) {
                                        clearTimeout(timeout);
                                        console.error("Captcha initialization error:", error);
                                        handleCaptchaError(error);
                                        if (initReject) {
                                            initReject(error);
                                            initResolve = null;
                                            initReject = null;
                                        }
                                    },
                                });
                            } catch (error) {
                                clearTimeout(timeout);
                                console.error("Error during captcha initialization:", error);
                                reject(error);
                            }
                        })
                        .catch(function (error) {
                            clearTimeout(timeout);
                            console.error("SDK loading failed:", error);
                            reject(error);
                        });
                });
            }

            // 移除阿里云验证码遮罩层
            function removeCaptchaMask() {
                // 查找并移除所有可能的遮罩层
                var maskSelectors = [
                    '.aliyunCaptcha-mask',
                    '#aliyunCaptcha-mask',
                    '.aliyun-captcha-mask',
                    '#aliyun-captcha-mask',
                    '.captcha-mask',
                    '#captcha-mask',
                    '[class*="aliyunCaptcha-mask"]',
                    '[id*="aliyunCaptcha-mask"]',
                    '[class*="captcha-mask"]',
                    '[id*="captcha-mask"]'
                ];

                maskSelectors.forEach(function(selector) {
                    try {
                        var elements = document.querySelectorAll(selector);
                        elements.forEach(function(element) {
                            element.style.display = 'none';
                            element.style.visibility = 'hidden';
                            element.style.opacity = '0';
                            element.style.background = 'transparent';
                            console.log('移除遮罩层:', selector);
                        });
                    } catch (error) {
                        // 忽略选择器错误
                    }
                });
            }

            // 验证成功回调
            function handleCaptchaSuccess(captchaVerifyParam) {
                setTimeout(()=>{
                    if(isError){
                        return;
                    }
                    console.log("🎉 Captcha verification success:", captchaVerifyParam);
                    // 移除遮罩层
                    removeCaptchaMask();
                    notifyNative("onCaptchaSuccess", {
                        captchaVerifyParam: captchaVerifyParam,
                    });
                },0)
            }

            // 验证失败回调
            function handleCaptchaFail(result) {
                console.error("❌ Captcha verification failed:", result);
                // 移除遮罩层，保持页面透明
                removeCaptchaMask();
                hideFullscreenLoading();
                notifyNative("onCaptchaFail", {
                    success: false,
                    error: "Captcha verification failed",
                    result: result,
                });
                isValidating = false;
            }

            // 验证关闭回调
            function handleCaptchaClose() {
                console.log("Captcha closed");
                // 移除遮罩层，保持页面透明
                removeCaptchaMask();
                notifyNative("onCaptchaCancelled", {
                    success: false,
                    error: "User cancelled",
                });
                isValidating = false;
            }

            // 验证错误回调
            function handleCaptchaError(error) {
                console.error("Captcha error:", error);
                isError = true;
                // 移除遮罩层，保持页面透明
                removeCaptchaMask();
                notifyNative("onCaptchaError", {
                    success: false,
                    error: "Captcha system error",
                    details: error,
                });
                isValidating = false;
            }
            // 通知原生端
            function notifyNative(method, data) {
                try {
                    // 通过统一的回调接口
                    if (window.NativeInterface && window.NativeInterface.onCaptchaCallback) {
                        window.NativeInterface.onCaptchaCallback(method, JSON.stringify(data));
                    }
                    console.log("notification sent:", method, data);
                } catch (error) {
                    console.error("Failed to notify Android:", error);
                }
            }

            // 销毁验证码
            function destroyCaptcha() {
                if (captcha) {
                    try {
                        captcha.destroyCaptcha();
                    } catch (error) {
                        console.error("Error destroying captcha:", error);
                    }
                    captcha = null;
                }
            }

            // 开始验证
            function startValidation() {
                console.log("startValidation called, isValidating:", isValidating);

                if (isValidating) {
                    console.log("Already validating, returning");
                    return;
                }

                isValidating = true;
                console.log("开始验证流程...");

                if (!captcha) {
                    console.log("Captcha not initialized, initializing...");
                    initCaptcha()
                        .then(function () {
                            console.log("Captcha initialized successfully, triggering validation");
                            // 验证码初始化成功后，直接触发验证弹窗
                            triggerCaptchaValidation();
                        })
                        .catch(function (error) {
                            console.error("Failed to initialize captcha:", error);
                            // 保持页面透明，不显示错误信息
                            isValidating = false;
                        });
                } else {
                    console.log("Captcha already initialized, showing captcha");
                    // 如果验证码已经初始化，直接显示
                    triggerCaptchaValidation();
                }
            }

            // 触发验证码验证（优化浏览器兼容性）
            function triggerCaptchaValidation() {
                console.log("🚀 Triggering captcha validation...");

                if (!captcha) {
                    console.error("❌ No captcha instance available");
                    // 保持页面透明，不显示错误信息
                    isValidating = false;
                    return;
                }
                // 尝试多种方式触发验证码
                try {
                    console.log("📱 Using button click trigger");
                    var captchaBtn = document.getElementById("button");
                    if (captchaBtn) {
                          captchaBtn.click();
                    } else {
                        console.error("❌ button element not found");
                        // 保持页面透明，不显示错误信息
                        isValidating = false;
                    }
                } catch (error) {
                    console.error("❌ Error triggering captcha:", error);
                    // 保持页面透明，不显示错误信息
                    isValidating = false;
                }
            }

            // 按钮点击处理函数
            function handleButtonClick(e) {
                console.log("Button clicked");
                if (!isValidating) {
                    e.preventDefault();
                    startValidation();
                }
            }

            // 设置语言（主要供原生端调用）
            function setLanguage(lang) {
                // 语言映射
                var languageMapping = {
                    CN: "cn",
                    EN: "en",
                    ES: "es",
                    PTBR: "ptbr",
                    PT: "ptbr",
                    RU: "ru",
                };

                currentLanguage = languageMapping[lang.toUpperCase()] || lang.toLowerCase() || "cn";
                console.log("语言设置为:", currentLanguage);

                // 如果验证码已初始化，需要重新初始化以应用新语言
                if (captcha) {
                    destroyCaptcha();
                }
            }

            // 设置环境（主要供原生端调用）
            function setEnvironment(env) {
                isDevelopment = env === "development" || env === "dev" || env === "test";
                console.log("环境设置为:", isDevelopment ? "development" : "production");
            }

            // 测试验证码函数（模仿Vue组件的popup方法）
            function testCaptcha() {
                console.log("🧪 Test captcha triggered");

                // 模仿Vue组件中的popup方法
                if (captcha && document.getElementById("validate-button")) {
                    console.log("🎯 Simulating Vue component popup method");
                    document.getElementById("validate-button").click();
                    return;
                }

                // 输出验证码实例的所有可用方法
                if (captcha) {
                    console.log("Captcha instance methods:", Object.getOwnPropertyNames(captcha));
                    console.log("Captcha instance:", captcha);

                    // 尝试直接调用验证码
                    if (typeof captcha.verify === "function") {
                        console.log("Calling captcha.verify()");
                        captcha.verify();
                    } else if (typeof captcha.showCaptcha === "function") {
                        console.log("Calling captcha.showCaptcha()");
                        captcha.showCaptcha();
                    } else {
                        console.log("No verify or showCaptcha method found, available methods:");
                        for (var prop in captcha) {
                            if (typeof captcha[prop] === "function") {
                                console.log("- " + prop);
                            }
                        }

                        // 尝试其他可能的方法
                        if (typeof captcha.popup === "function") {
                            console.log("Trying captcha.popup()");
                            captcha.popup();
                        }
                    }
                } else {
                    console.log("❌ No captcha instance available");
                    // 尝试重新初始化
                    startValidation();
                }
            }

            // 暴露给原生端的接口
            window.CaptchaWebView = {
                startValidation: startValidation,
                setLanguage: setLanguage,
                setEnvironment: setEnvironment,
                destroyCaptcha: destroyCaptcha,
                testCaptcha: testCaptcha,
                getStatus: function () {
                    return {
                        isValidating: isValidating,
                        hasCaptcha: !!captcha,
                        currentLanguage: currentLanguage,
                        isDevelopment: isDevelopment,
                    };
                },
            };

            // 暴露测试函数到全局
            window.testCaptcha = testCaptcha;

            // 模仿Tool.loadJS的加载方式
            function loadJS(url) {
                return new Promise(function(resolve, reject) {
                    const d = document;
                    let s = d.createElement('script');
                    s.type = 'text/javascript';
                    s.charset = 'utf-8';

                    if (s.readyState) {
                        // IE
                        s.onreadystatechange = function () {
                            if (s.readyState === 'loaded' || s.readyState === 'complete') {
                                s.onreadystatechange = null;
                                resolve();
                            }
                        };
                    } else {
                        s.onload = function () {
                            resolve();
                        };
                        s.onerror = function(error) {
                            reject(error);
                        };
                    }
                    s.src = url;
                    d.getElementsByTagName('head')[0].appendChild(s);
                });
            }

            // 移除vConsole调试功能，保持页面完全透明

            // 确保SDK加载（模仿Vue组件的方式）
            function ensureSDKLoaded(retryCount = 0) {
                return new Promise(function (resolve, reject) {
                    if (window.initAliyunCaptcha) {
                        console.log("✅ AliyunCaptcha SDK already loaded");
                        resolve();
                        return;
                    }

                    const maxRetries = 3;
                    const retryDelay = 2000;

                    console.log(`🔄 Loading AliyunCaptcha SDK (attempt ${retryCount + 1}/${maxRetries})...`);
                    console.log("📥 下载无痕验证，重新下载");

                    loadJS('https://o.alicdn.com/captcha-frontend/aliyunCaptcha/AliyunCaptcha.js')
                        .then(function() {
                            console.log("✅ AliyunCaptcha SDK loaded successfully");
                            // 等待SDK完全初始化
                            setTimeout(function () {
                                if (window.initAliyunCaptcha) {
                                    console.log("🎯 SDK initialization completed");
                                    resolve();
                                } else {
                                    console.error("❌ SDK loaded but initAliyunCaptcha not available");
                                    reject(new Error("SDK loaded but initAliyunCaptcha not available"));
                                }
                            }, 300);
                        })
                        .catch(function(error) {
                            console.error("❌ Failed to load AliyunCaptcha SDK:", error);
                            if (retryCount < maxRetries - 1) {
                                console.log(`🔄 Retrying SDK load in ${retryDelay}ms...`);
                                setTimeout(function() {
                                    ensureSDKLoaded(retryCount + 1).then(resolve).catch(reject);
                                }, retryDelay);
                            } else {
                                reject(new Error("Failed to load SDK after " + maxRetries + " attempts"));
                            }
                        });
                });
            }

            // 浏览器类型检测
            function getBrowserInfo() {
                const ua = navigator.userAgent;
                const isAndroid = /Android/i.test(ua);
                const isWeChat = /MicroMessenger/i.test(ua);
                const isAndroidBrowser = isAndroid && /Android.*Browser/i.test(ua);
                const isChrome = /Chrome/i.test(ua) && !/Edge/i.test(ua);
                const isSafari = /Safari/i.test(ua) && !/Chrome/i.test(ua);

                return {
                    isAndroid,
                    isWeChat,
                    isAndroidBrowser,
                    isChrome,
                    isSafari,
                    browserName: isWeChat ? 'WeChat' :
                                isAndroidBrowser ? 'AndroidBrowser' :
                                isChrome ? 'Chrome' :
                                isSafari ? 'Safari' : 'Unknown'
                };
            }

            // 移除设备信息检测功能，保持页面完全透明

            // 网络状态检测
            function checkNetworkStatus() {
                if (navigator.onLine) {
                    console.log("📡 Network: Online");
                    return true;
                } else {
                    console.error("📵 Network: Offline - Please check your internet connection");
                    // 保持页面透明，不显示网络错误信息
                    return false;
                }
            }

            // 监听网络状态变化
            window.addEventListener('online', function() {
                console.log("🌐 Network restored");
            });

            window.addEventListener('offline', function() {
                console.error("🌐 Network lost");
                // 保持页面透明，不显示网络错误信息
            });

            // 重写console.clear，防止清空日志
            (function() {
                var originalConsoleClear = console.clear;
                console.clear = function() {
                    console.log("⚠️ console.clear() 被阻止 - 保留调试日志");
                    // 如果需要真的清空，可以注释掉这行
                    // originalConsoleClear.apply(console, arguments);
                };

                // 暴露恢复方法（仅开发环境）
                window.restoreConsoleClear = function() {
                    console.clear = originalConsoleClear;
                    console.log("✅ console.clear() 已恢复原始功能");
                };
            })();

            // 页面加载完成后初始化
            document.addEventListener("DOMContentLoaded", function () {
                console.log("DOM loaded, initializing...");

                // 显示Loading状态
                showFullscreenLoading();

                // 从地址栏获取配置
                var urlConfig = getUrlConfig();

                // 应用配置
                currentLanguage = urlConfig.language;
                isDevelopment = urlConfig.isDevelopment;

                console.log("最终配置:", {
                    语言: currentLanguage,
                    环境: isDevelopment ? "development" : "production",
                });

                // 页面保持透明，不需要绑定任何按钮事件
                console.log("页面已设置为透明模式，只有验证码弹窗会显示");

                // 确保SDK加载完成
                console.log("开始加载验证码SDK...");
                ensureSDKLoaded()
                    .then(function () {
                        console.log("SDK ready, captcha can be initialized");
                        // 直接开始验证，不显示任何界面元素
                        setTimeout(function() {
                            startValidation();
                        }, 100);
                    })
                    .catch(function (error) {
                        console.error("SDK loading failed:", error);
                        // 保持页面透明，不显示任何错误信息
                    });

                console.log("Captcha WebView initialized");
            });

            // 页面卸载时清理
            window.addEventListener("beforeunload", function () {
                destroyCaptcha();
            });
        </script>
    </body>
</html>

