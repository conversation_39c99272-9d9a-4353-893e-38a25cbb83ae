export const EMOJI_LIST = [
    '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '😤',
    '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚',
    '😋', '😛', '😜', '🤪', '😝', '🤑', '🤗', '🤭', '🤫', '🤔',
    '🤐', '🤨', '😐', '😑', '😶', '😏', '😒', '🙄', '😬', '🤥',
    '😌', '😔', '😪', '🤤', '😴', '😷', '🤒', '🤕', '🤢', '🤮',
    '🤧', '😵', '🤯', '🤠', '😎', '🤓', '🧐', '😕', '😟', '🙁',
    '😮', '😯', '😲', '😳', '🥺', '😦', '😧', '😨', '😰', '😥',
    '😢', '😭', '😱', '😖', '😣', '😞', '😓', '😩', '😫', '🥱',
    '❤️', '😡', '😠', '🤬', '🤯', '😈', '👿', '💀', '☠️', '💩',
    '🤡', '👹', '👺', '👻', '👽', '👾', '🤖', '😺', '😸', '😹',
    '😻', '😼', '😽', '🙀', '😿', '😾', '🙈', '🙉', '🙊', '💋',
]
export const CHAT_TYPE = {
    CHAT_WINDOW: 1, //聊天窗口
    CHAT_HISTORY: 2,//聊天历史记录
};
export const EXAM_TYPE = {
    OBSTETRICS: 0, // 产科
    GYNECOLOGY: 1, // 妇科
    CARDIOVASCULAR: 2, // 心脏
    VASCULAR: 3, // 血管
    ABDOMEN: 4, // 腹部
    UROLOGY: 5, // 泌尿科
    SMALL_ORGAN: 6, // 小器官
    PEDIATRICS: 7, // 儿科
    BREAST: 8, // 乳腺
    UNKNOWN: 9, // 未知
    UNDEFINED: 10, // 未定义
    ALL: -1, // 全部
}

// 应用领域枚举
export const APPLICATION_AREA = Object.freeze({
    MEDICAL_INSTITUTION: 1,                 // 医疗机构
    VENDOR_PERSONNEL: 2,                    // 厂商人员
    MEDICAL_SCHOOL: 3,                      // 医学院校
    OTHER: 4,                              // 其他
    1: 'MEDICAL_INSTITUTION',
    2: 'VENDOR_PERSONNEL',
    3: 'MEDICAL_SCHOOL',
    4: 'OTHER',
});

// 职业身份枚举
export const PROFESSIONAL_IDENTITY = Object.freeze({
    PHYSICIAN: 1,                           // 医生
    SONOGRAPHER: 2,                         // 超声技师/超声技术员
    ADMINISTRATOR: 3,                       // 管理员
    RESEARCHER: 4,                          // 研究员
    TECHNICAL_SUPPORT: 5,                   // 技术支持
    OTHER_VENDOR: 6,                        // 厂商其他人员
    OTHER_MEDICAL: 7,                       // 其他医疗领域相关用户
    TEACHER: 8,                            // 老师
    STUDENT: 9,                            // 学生
    OTHER_MEDICAL_SCHOOL: 10,              // 其他医学院人员
    1: 'PHYSICIAN',
    2: 'SONOGRAPHER',
    3: 'ADMINISTRATOR',
    4: 'RESEARCHER',
    5: 'TECHNICAL_SUPPORT',
    6: 'OTHER_VENDOR',
    7: 'OTHER_MEDICAL',
    8: 'TEACHER',
    9: 'STUDENT',
    10: 'OTHER_MEDICAL_SCHOOL',
});

// 根据应用领域获取对应的职业身份选项
export const PROFESSIONAL_IDENTITY_BY_APPLICATION_AREA = Object.freeze({
    [APPLICATION_AREA.MEDICAL_INSTITUTION]: [
        PROFESSIONAL_IDENTITY.PHYSICIAN,
        PROFESSIONAL_IDENTITY.SONOGRAPHER,
        PROFESSIONAL_IDENTITY.RESEARCHER,
        PROFESSIONAL_IDENTITY.OTHER_MEDICAL
    ],
    [APPLICATION_AREA.VENDOR_PERSONNEL]: [
        PROFESSIONAL_IDENTITY.ADMINISTRATOR,
        PROFESSIONAL_IDENTITY.TECHNICAL_SUPPORT,
        PROFESSIONAL_IDENTITY.OTHER_VENDOR
    ],
    [APPLICATION_AREA.MEDICAL_SCHOOL]: [
        PROFESSIONAL_IDENTITY.TEACHER,
        PROFESSIONAL_IDENTITY.STUDENT,
        PROFESSIONAL_IDENTITY.OTHER_MEDICAL_SCHOOL
    ],
    [APPLICATION_AREA.OTHER]: [] // 其他情况下不显示任何选项
});

// 菜单项常量
export const MENU_ITEMS = Object.freeze({
    // AI分析
    SEND_TO_ANALYZE: 0,
    // 转发
    TRANSMIT: 1,
    // 收藏
    SAVE_FAVORITE: 2,
    // 分享微信
    SHARE_WECHAT: 3,
    // 多选
    MULTI_SELECT: 4,
    // 设置检查信息
    SET_EXAM_INFO: 5,
    // 编辑审查信息
    EDIT_REVIEW_INFO: 6,
    // 撤回消息
    WITHDRAW: 7,
    // 复制
    COPY: 8,
    // 删除
    DELETE: 9,
    // 邮箱分享
    SHARE_EMAIL: 10,
    // 图片搜索
    IMAGE_SEARCH: 11,
    // 重命名
    RENAME: 12,
    // 删除检查
    DELETE_EXAM: 13,
    // 引用
    QUOTE: 14,
    // 定位原消息
    LOCATE_ORIGINAL: 15
});

// 菜单项配置（包含key、名称、事件）
export const MENU_ITEM_CONFIG = Object.freeze([
    {
        key: MENU_ITEMS.SEND_TO_ANALYZE,
        name: 'action_analyze_text',
        event: 'sendToAnalyze'
    },
    {
        key: MENU_ITEMS.TRANSMIT,
        name: 'transmit_title',
        event: 'transmit'
    },
    {
        key: MENU_ITEMS.SAVE_FAVORITE,
        name: 'action_favorite_text',
        event: 'saveFavorite'
    },
    {
        key: MENU_ITEMS.SHARE_WECHAT,
        name: 'share_to_wechat',
        event: 'shareToWechat'
    },
    {
        key: MENU_ITEMS.MULTI_SELECT,
        name: 'multi_select',
        event: 'multiSelectImage'
    },
    {
        key: MENU_ITEMS.SET_EXAM_INFO,
        name: 'action_set_exam_info_text',
        event: 'setResourceExamInfo'
    },
    {
        key: MENU_ITEMS.EDIT_REVIEW_INFO,
        name: 'edit_review_info',
        event: 'editReviewInfo'
    },
    {
        key: MENU_ITEMS.WITHDRAW,
        name: 'revocation_message',
        event: 'revocationMessage'
    },
    {
        key: MENU_ITEMS.COPY,
        name: 'copy',
        event: 'copyText'
    },
    {
        key: MENU_ITEMS.DELETE,
        name: 'action_delete_text',
        event: 'deleteChatMessages'
    },
    {
        key: MENU_ITEMS.SHARE_EMAIL,
        name: 'share_email_title',
        event: 'shareToEmail'
    },
    {
        key: MENU_ITEMS.IMAGE_SEARCH,
        name: 'searc_in_case_database',
        event: 'searchInCaseData'
    },
    {
        key: MENU_ITEMS.RENAME,
        name: 'rename',
        event: 'imageRename'
    },
    {
        key: MENU_ITEMS.DELETE_EXAM,
        name: 'delete_case',
        event: 'deleteExam'
    },
    {
        key: MENU_ITEMS.QUOTE,
        name: 'quote_title',
        event: 'quoteMessage'
    },
    {
        key: MENU_ITEMS.LOCATE_ORIGINAL,
        name: 'locate_original_message',
        event: 'locateOriginalMessage'
    }
]);
