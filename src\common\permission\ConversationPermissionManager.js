import BasePermissionManager from './BasePermissionManager.js';
import { GROUP_ROLE } from './constant.js';

/**
 * 会话权限管理器
 * 负责聊天会话内的角色权限控制（群主、管理员、普通成员等）
 */
class ConversationPermissionManager extends BasePermissionManager {
    constructor() {
        super();
        this.conversationRoles = new Map(); // 会话角色缓存 {conversationId: {userId: role}}
        this.conversationPermissions = new Map(); // 会话权限配置
        this.loadConversationPermissions();
    }

    /**
     * 加载权限数据
     */
    async loadPermissions() {
        this.loadConversationPermissions();
    }

    /**
     * 加载会话权限配置
     */
    loadConversationPermissions() {
        // 定义会话内的权限配置
        const conversationPermissions = {
            // 消息相关权限
            'message': {
                'send': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER, GROUP_ROLE.NORMAL] },
                'delete': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER], conditions: ['own_message'], strategy: 'OR' },
                'withdraw': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER, GROUP_ROLE.NORMAL], conditions: ['own_message', 'time_limit'] },
                'pin': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER] },
                'forward': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER, GROUP_ROLE.NORMAL] },
                'quote': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER, GROUP_ROLE.NORMAL] },
                'transmit': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER, GROUP_ROLE.NORMAL] },
                'copy': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER, GROUP_ROLE.NORMAL] }
            },
            // 成员管理权限
            'member': {
                'invite': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER] },
                'remove': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER] },
                'mute': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER] },
                'set_admin': { roles: [GROUP_ROLE.CREATOR] },
                'view_list': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER, GROUP_ROLE.NORMAL] }
            },
            // 会话设置权限
            'conversation': {
                'edit_subject': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER] },
                'edit_announcement': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER] },
                'edit_settings': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER] },
                'delete': { roles: [GROUP_ROLE.CREATOR] },
                'transfer_ownership': { roles: [GROUP_ROLE.CREATOR] }
            },
            // 文件和资源权限
            'resource': {
                'edit': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER], conditions: ['own_resource','own_message'], strategy: 'OR' },
                'download': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER, GROUP_ROLE.NORMAL] },
                'delete': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER], conditions: ['own_resource','own_message'], strategy: 'OR' },
                'share': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER, GROUP_ROLE.NORMAL] },
                'rename': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER], conditions: ['own_message'], strategy: 'OR' },
                'set_exam_info': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER, GROUP_ROLE.NORMAL] },
                'save_favorite': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER, GROUP_ROLE.NORMAL] },
                'share_wechat': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER, GROUP_ROLE.NORMAL] },
                'share_email': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER, GROUP_ROLE.NORMAL] },
                'multi_select': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER, GROUP_ROLE.NORMAL] }
            },
            // 直播
            'conference': {
                'live_detail': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER], conditions: ['own_message'], strategy: 'OR' },
                'stop': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER] },
                'end': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER] },
                'mute_all': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER] },
                'control_camera': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER] },
                'screen_share': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER, GROUP_ROLE.NORMAL] }
            },
            // 检查和分析权限
            'exam': {
                'delete': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER], conditions: ['own_message'], strategy: 'OR' },
                'send_to_analyze': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER, GROUP_ROLE.NORMAL] },
                'image_search': { roles: [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER, GROUP_ROLE.NORMAL] }
            }
        };
        Object.entries(conversationPermissions).forEach(([feature, permissions]) => {
            this.conversationPermissions.set(feature, permissions);
        });
    }

    /**
     * 设置用户在会话中的角色
     * @param {string} conversationId - 会话ID
     * @param {string} userId - 用户ID
     * @param {string} role - 角色 (owner|admin|member|muted)
     */
    setUserConversationRole(conversationId, userId, role) {
        // 统一转换为数字类型，确保与存储的 key 类型一致
        const cid = Number(conversationId);
        const uid = Number(userId);

        // 获取旧角色用于比较
        const oldRole = this.getUserConversationRole(conversationId, userId);

        if (!this.conversationRoles.has(cid)) {
            this.conversationRoles.set(cid, new Map());
        }
        this.conversationRoles.get(cid).set(uid, role);

        // 清除相关缓存
        this.clearConversationCache(conversationId);

        // 如果角色发生变化，触发事件
        if (oldRole !== role) {
            this.emitConversationPermissionChange({
                conversationId: cid,
                userId: uid,
                oldRole,
                newRole: role,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 获取用户在会话中的角色
     * @param {string} conversationId - 会话ID
     * @param {string} userId - 用户ID
     * @returns {string} 角色
     */
    getUserConversationRole(conversationId, userId) {

        // 统一转换为数字类型，确保与存储的 key 类型一致
        const cid = Number(conversationId);
        const uid = Number(userId);
        const conversationRoles = this.conversationRoles.get(cid);

        if (!conversationRoles) {
            return GROUP_ROLE.NORMAL; // 默认为普通成员
        }

        const userRole = conversationRoles.get(uid);
        return userRole !== undefined ? userRole : GROUP_ROLE.NORMAL;
    }

    /**
     * 批量设置会话成员角色
     * @param {string} conversationId - 会话ID
     * @param {Object} memberRoles - 成员角色映射 {userId: role}
     */
    setConversationMemberRoles(conversationId, memberRoles) {
        const conversationRoleMap = new Map();
        const roleChanges = [];

        for (const [userId, role] of Object.entries(memberRoles)) {
            const oldRole = this.getUserConversationRole(conversationId, userId);
            conversationRoleMap.set(userId, role);

            // 记录角色变化
            if (oldRole !== role) {
                roleChanges.push({
                    userId: Number(userId),
                    oldRole,
                    newRole: role
                });
            }
        }

        this.conversationRoles.set(conversationId, conversationRoleMap);
        this.clearConversationCache(conversationId);

        // 如果有角色变化，触发事件
        if (roleChanges.length > 0) {
            this.emitConversationPermissionChange({
                conversationId: Number(conversationId),
                roleChanges,
                timestamp: Date.now()
            });
        }
    }

    /**
     * 检查会话权限
     * @param {string} permission - 权限标识 (格式: feature.action)
     * @param {Object} context - 上下文信息
     * @param {string} context.conversationId - 会话ID
     * @param {string} context.userId - 用户ID (可选，默认使用当前用户)
     * @param {string} context.targetUserId - 目标用户ID (用于某些操作)
     * @param {string} context.messageId - 消息ID (用于消息相关操作)
     * @param {string} context.resourceId - 资源ID (用于资源相关操作)
     * @returns {boolean} 是否有权限
     */
    hasPermission(permission, context = {}) {

        if (!this.isInitialized()) {
            console.warn('ConversationPermissionManager not initialized');
            return false;
        }

        const { conversationId, userId = this.getUserId() } = context;

        if (!conversationId) {
            console.warn('ConversationPermissionManager: conversationId is required');
            return false;
        }

        // 解析权限标识
        const [feature, action] = permission.split('.');
        if (!feature || !action) {
            console.warn('ConversationPermissionManager: invalid permission format, expected "feature.action"');
            return false;
        }

        // 使用缓存机制包装会话权限检查
        return this.checkWithCache('conversation', permission, context, () => {
            return this.checkConversationPermission(feature, action, context);
        });
    }

    /**
     * 检查会话权限
     * @param {string} feature - 功能名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkConversationPermission(feature, action, context = {}) {
        const { conversationId, userId = this.getUserId() } = context;
        console.log('checkConversationPermission', feature, action, context);

        // 获取用户在会话中的角色
        const userRole = this.getUserConversationRole(conversationId, userId);
        console.log('userRole', userRole);

        // 获取权限配置
        const permissionConfig = this.getPermissionConfig(feature, action);
        if (!permissionConfig) {
            return false; // 没有配置默认不允许
        }

        // 检查策略，默认为AND
        const strategy = permissionConfig.strategy || 'AND';

        // 检查角色权限
        const hasRolePermission = this.checkRolePermission(userRole, permissionConfig.roles);

        // 检查特殊条件
        const hasConditionPermission = permissionConfig.conditions ?
            this.checkPermissionConditions(permissionConfig.conditions, context, strategy) : true;

        // 根据策略返回结果
        if (strategy === 'OR') {
            // OR策略：角色权限或条件权限满足其一即可
            return hasRolePermission || (permissionConfig.conditions && hasConditionPermission);
        } else {
            // AND策略：角色权限和条件权限都必须满足
            return hasRolePermission && hasConditionPermission;
        }
    }

    /**
     * 获取权限配置
     * @param {string} feature - 功能名称
     * @param {string} action - 操作名称
     * @returns {Object|null} 权限配置
     */
    getPermissionConfig(feature, action) {
        const featurePermissions = this.conversationPermissions.get(feature);
        return featurePermissions?.[action] || null;
    }

    /**
     * 检查角色权限
     * @param {string} userRole - 用户角色
     * @param {Array} allowedRoles - 允许的角色列表
     * @returns {boolean} 是否有权限
     */
    checkRolePermission(userRole, allowedRoles = []) {
        return allowedRoles.includes(userRole);
    }

    /**
     * 检查权限条件
     * @param {Array} conditions - 条件列表
     * @param {Object} context - 上下文信息
     * @param {string} strategy - 策略 ('AND' | 'OR')，默认为 'AND'
     * @returns {boolean} 是否满足条件
     */
    checkPermissionConditions(conditions, context, strategy = 'AND') {
        const checkCondition = (condition) => {
            switch (condition) {
            case 'own_message':
                return this.checkOwnMessage(context);
            case 'own_resource':
                return this.checkOwnResource(context);
            case 'time_limit':
                return this.checkTimeLimit(context);
            default:
                return true;
            }
        };

        if (strategy === 'OR') {
            // OR策略：任意一个条件满足即可
            return conditions.some(checkCondition);
        } else {
            // AND策略：所有条件都必须满足
            return conditions.every(checkCondition);
        }
    }

    /**
     * 检查是否为自己的消息
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否为自己的消息
     */
    checkOwnMessage(context) {
        const { message, userId = this.getUserId() } = context;

        // 如果直接提供了message对象，使用message对象检查
        if (message) {
            console.error('checkOwnMessage', message, userId);
            // 检查消息的发送者ID
            const senderId = message.sender_id || message.senderId || message.uid;
            return senderId && String(senderId) === String(userId);
        }
        return false;
    }

    /**
     * 检查是否为自己的资源
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否为自己的资源
     */
    checkOwnResource(context) {
        const { message, userId = this.getUserId() } = context;

        // 如果直接提供了resource对象，使用resource对象检查
        if (message) {
            const creatorId = message.creator_id || message.creatorId;
            return creatorId && String(creatorId) === String(userId);
        }

        return false;
    }

    /**
     * 检查时间限制（如撤回消息的时间限制）
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否在时间限制内
     */
    checkTimeLimit(context) {
        const { messageTime, timeLimit = 2 * 60 * 1000 } = context; // 默认2分钟
        if (!messageTime) {
            return true;
        }

        const now = Date.now();
        return (now - messageTime) <= timeLimit;
    }

    /**
     * 清除会话缓存
     * @param {string} conversationId - 会话ID
     */
    clearConversationCache(conversationId) {
        // 清除特定会话的权限缓存
        const cacheKeys = Array.from(this.permissions.keys()).filter(key =>
            key.startsWith(`conversation_${conversationId}_`)
        );
        cacheKeys.forEach(key => this.permissions.delete(key));
    }

    /**
     * 触发会话权限变化事件
     * @param {Object} changeInfo - 变化信息
     */
    emitConversationPermissionChange(changeInfo) {
        // 只触发专门的会话权限变化事件，不触发通用权限变化事件
        // 这样可以避免在 attendeesUpdate 时触发其他不必要的 change 事件
        if (typeof window !== 'undefined') {
            const event = new CustomEvent('permission:conversationChanged', {
                detail: changeInfo
            });
            window.dispatchEvent(event);
        }
    }

    /**
     * 获取用户在会话中的权限列表
     * @param {string} conversationId - 会话ID
     * @param {string} userId - 用户ID
     * @returns {Object} 权限列表
     */
    getUserConversationPermissions(conversationId, userId = this.getUserId()) {
        const permissions = {};

        // 遍历所有功能的权限配置
        for (const [feature, actions] of this.conversationPermissions.entries()) {
            permissions[feature] = {};
            for (const [action] of Object.entries(actions)) {
                permissions[feature][action] = this.checkConversationPermission(
                    feature,
                    action,
                    { conversationId, userId }
                );
            }
        }

        return permissions;
    }

    /**
     * 检查是否为会话群主
     * @param {string} conversationId - 会话ID
     * @param {string} userId - 用户ID
     * @returns {boolean} 是否为群主
     */
    isConversationOwner(conversationId, userId = this.getUserId()) {
        return this.getUserConversationRole(conversationId, userId) === GROUP_ROLE.CREATOR;
    }

    /**
     * 检查是否为会话管理员（包括群主）
     * @param {string} conversationId - 会话ID
     * @param {string} userId - 用户ID
     * @returns {boolean} 是否为管理员
     */
    isConversationAdmin(conversationId, userId = this.getUserId()) {
        const role = this.getUserConversationRole(conversationId, userId);
        return [GROUP_ROLE.CREATOR, GROUP_ROLE.MANAGER].includes(role);
    }

    /**
     * 基于现有数据结构解析会话成员角色（参考 common_base.js）
     * @param {Object} conversationData - 会话数据
     * @returns {Object} 成员角色映射 {userId: role}
     */
    parseConversationMemberRoles(conversationData) {
        const memberRoles = {};
        const { attendeeList, creator_id } = conversationData;

        if (!attendeeList) {
            return memberRoles;
        }

        // 遍历参与者列表
        for (const attendeeKey in attendeeList) {
            const attendee = attendeeList[attendeeKey];
            if (!attendee || (!attendee.uid && !attendee.userid)) {
                continue;
            }

            const userId = attendee.uid || attendee.userid;

            // 确定用户角色，参考 common_base.js 中的逻辑和 systemConfig.groupRole
            let role = GROUP_ROLE.NORMAL; // 默认为普通成员

            // 检查是否为群主（creator）
            if (userId === creator_id) {
                role = GROUP_ROLE.CREATOR; // 群主
            }else if (attendee.role === GROUP_ROLE.CREATOR) { // groupRole.creator        // 检查群内角色（参考 systemConfig.groupRole）
                role = GROUP_ROLE.CREATOR; // 群主
            } else if (attendee.role === GROUP_ROLE.MANAGER) { // groupRole.manager
                role = GROUP_ROLE.MANAGER; // 管理员
            } else if (attendee.role === GROUP_ROLE.NORMAL) { // groupRole.normal
                role = GROUP_ROLE.NORMAL; // 普通成员
            }

            // 移除被禁言用户的处理，因为不存在 muted 角色

            memberRoles[userId] = role;
        }

        return memberRoles;
    }

    /**
     * 检查用户是否为会话创建者（参考 common_base.js 的 checkIsCreator）
     * @param {string} conversationId - 会话ID
     * @param {string} userId - 用户ID
     * @param {Object} conversationData - 会话数据（可选，如果不提供则从store获取）
     * @returns {boolean} 是否为创建者
     */
    checkIsCreator(conversationId, userId, conversationData = null) {
        if (conversationData) {
            return conversationData.creator_id === userId && conversationData.is_single_chat === 0;
        }

        // 如果没有提供会话数据，从角色缓存中判断
        const userRole = this.getUserConversationRole(conversationId, userId);
        return userRole === GROUP_ROLE.CREATOR;
    }

    /**
     * 检查用户是否为会话管理员（参考 common_base.js 的 checkIsManager）
     * @param {string} conversationId - 会话ID
     * @param {string} userId - 用户ID
     * @param {Object} conversationData - 会话数据（可选）
     * @returns {boolean} 是否为管理员
     */
    checkIsManager(conversationId, userId, conversationData = null) {
        if (conversationData && conversationData.attendeeList) {
            // 参考 common_base.js 的逻辑
            for (const key in conversationData.attendeeList) {
                const item = conversationData.attendeeList[key];
                if (item.role === GROUP_ROLE.MANAGER && (item.userid === userId || item.uid === userId)) { // groupRole.manager
                    return true;
                }
            }
            return false;
        }

        // 从角色缓存中判断
        const userRole = this.getUserConversationRole(conversationId, userId);
        return userRole === GROUP_ROLE.MANAGER;
    }

    /**
     * 检查用户是否为管理员或创建者（参考 common_base.js 的 checkIsManagerOrCreator）
     * @param {string} conversationId - 会话ID
     * @param {string} userId - 用户ID
     * @param {Object} conversationData - 会话数据（可选）
     * @returns {boolean} 是否为管理员或创建者
     */
    checkIsManagerOrCreator(conversationId, userId, conversationData = null) {
        if (conversationData && conversationData.attendeeList) {
            // 参考 common_base.js 的逻辑
            for (const key in conversationData.attendeeList) {
                const item = conversationData.attendeeList[key];
                if ([GROUP_ROLE.MANAGER, GROUP_ROLE.CREATOR].includes(item.role) && (item.userid === userId || item.uid === userId)) { // manager or creator
                    return true;
                }
            }
            return false;
        }

        // 从角色缓存中判断
        const userRole = this.getUserConversationRole(conversationId, userId);
        return [GROUP_ROLE.MANAGER, GROUP_ROLE.CREATOR].includes(userRole);
    }

    /**
     * 从会话数据初始化权限（集成到现有系统）
     * @param {string} conversationId - 会话ID
     * @param {Object} conversationData - 会话数据
     */
    initFromConversationData(conversationId, conversationData) {
        if (!conversationData) {
            console.warn('ConversationPermissionManager: No conversation data provided');
            return;
        }

        // 解析成员角色
        const memberRoles = this.parseConversationMemberRoles(conversationData);

        // 设置会话成员角色
        this.setConversationMemberRoles(conversationId, memberRoles);

        console.log(`[ConversationPermissionManager] Initialized permissions for conversation ${conversationId}`, memberRoles);
    }

    /**
     * 更新单个用户的会话角色（基于 attendee 数据）
     * @param {string} conversationId - 会话ID
     * @param {Object} attendeeData - 参与者数据
     */
    updateUserRoleFromAttendeeData(conversationId, attendeeData) {
        if (!attendeeData || (!attendeeData.uid && !attendeeData.userid)) {
            return;
        }

        const userId = attendeeData.uid || attendeeData.userid;
        let role = GROUP_ROLE.NORMAL;

        // 根据 attendee 数据确定角色
        if (attendeeData.role === GROUP_ROLE.CREATOR) { // groupRole.creator
            role = GROUP_ROLE.CREATOR;
        } else if (attendeeData.role === GROUP_ROLE.MANAGER) { // groupRole.manager
            role = GROUP_ROLE.MANAGER;
        } else if (attendeeData.role === GROUP_ROLE.NORMAL) { // groupRole.normal
            role = GROUP_ROLE.NORMAL;
        }

        // 移除被禁言用户的处理，因为不存在 muted 角色

        this.setUserConversationRole(conversationId, userId, role);
    }

    /**
     * 销毁会话权限管理器
     */
    destroy() {
        this.conversationRoles.clear();
        this.conversationPermissions.clear();
        super.destroy();
    }
}

export default ConversationPermissionManager;
